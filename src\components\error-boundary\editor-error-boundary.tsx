'use client'

import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON>w, Save, Home } from 'lucide-react'
import { logger } from '@/lib/services/logger'

interface Props {
  children: React.ReactNode
  onReset?: () => void
  projectId?: string
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

export class EditorErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    logger.error('Editor error boundary caught error', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      projectId: this.props.projectId
    })

    this.setState({
      error,
      errorInfo
    })

    // Save current work to localStorage as backup
    try {
      const editorContent = localStorage.getItem('editor-content')
      if (editorContent) {
        const backup = {
          content: editorContent,
          timestamp: new Date().toISOString(),
          error: error.message
        }
        localStorage.setItem('editor-backup', JSON.stringify(backup))
      }
    } catch (e) {
      logger.error('Failed to save editor backup', e)
    }
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null })
    this.props.onReset?.()
  }

  handleSaveBackup = () => {
    try {
      const backup = localStorage.getItem('editor-backup')
      if (backup) {
        const { content, timestamp } = JSON.parse(backup)
        const blob = new Blob([content], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `backup-${new Date(timestamp).getTime()}.txt`
        a.click()
        URL.revokeObjectURL(url)
      }
    } catch (e) {
      logger.error('Failed to download backup', e)
    }
  }

  render() {
    if (this.state.hasError) {
      const isNetworkError = this.state.error?.message?.toLowerCase().includes('network')
      const isMemoryError = this.state.error?.message?.toLowerCase().includes('memory')

      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <Card className="max-w-lg w-full">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-destructive" />
              </div>
              <CardTitle>Editor Error</CardTitle>
              <CardDescription>
                {isNetworkError 
                  ? "We're having trouble connecting to our servers"
                  : isMemoryError
                  ? "The editor ran out of memory"
                  : "Something went wrong with the editor"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertDescription>
                  Don't worry - your work has been automatically saved. You can continue where you left off.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Button 
                  onClick={this.handleReset}
                  className="w-full"
                  variant="default"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reload Editor
                </Button>

                <Button 
                  onClick={this.handleSaveBackup}
                  className="w-full"
                  variant="outline"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Download Backup
                </Button>

                <Button 
                  onClick={() => window.location.href = '/dashboard'}
                  className="w-full"
                  variant="ghost"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </div>

              {process.env.NODE_ENV === 'development' && (
                <details className="mt-4">
                  <summary className="text-sm text-muted-foreground cursor-pointer">
                    Error Details
                  </summary>
                  <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                    {this.state.error?.stack}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}