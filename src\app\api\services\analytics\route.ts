import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import ServiceManager from '@/lib/services/service-manager';
import { authenticateUser } from '@/lib/auth';
import { generalLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter';
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for analytics operations
    const clientIP = getClientIP(request);
    const rateLimitResult = generalLimiter.check(60, clientIP); // 60 requests per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    // Authentication required for analytics access
    const authResult = await authenticateUser();
    if (!authResult.success) {
      return authResult.response!;
    }

    const body = await request.json();
    const { action, ...params } = body;
    
    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const analytics = await serviceManager.getAnalyticsEngine();
    
    if (!analytics) {
      return NextResponse.json(
        { error: 'Analytics Engine service not available' },
        { status: 503 }
      );
    }

    switch (action) {
      case 'track_event':
        const trackResult = await serviceManager.trackAnalytics(params);
        return NextResponse.json({ success: trackResult });

      case 'track_behavior':
        const behaviorResult = await analytics.trackUserBehavior(
          params.userId,
          params.sessionId,
          params.action,
          params.data
        );
        return NextResponse.json(behaviorResult);

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    logger.error('Analytics API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    const userId = url.searchParams.get('userId');
    const projectId = url.searchParams.get('projectId');
    const timeframe = url.searchParams.get('timeframe') as 'week' | 'month' | 'quarter';
    const days = url.searchParams.get('days');
    
    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const analytics = await serviceManager.getAnalyticsEngine();
    
    if (!analytics) {
      return NextResponse.json(
        { error: 'Analytics Engine service not available' },
        { status: 503 }
      );
    }

    switch (type) {
      case 'user_insights':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId parameter required' },
            { status: 400 }
          );
        }
        const insightsResult = await analytics.getUserInsights(userId);
        return NextResponse.json(insightsResult);

      case 'project_analytics':
        if (!projectId) {
          return NextResponse.json(
            { error: 'projectId parameter required' },
            { status: 400 }
          );
        }
        const projectResult = await analytics.getProjectAnalytics(projectId);
        return NextResponse.json(projectResult);

      case 'writing_heatmap':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId parameter required' },
            { status: 400 }
          );
        }
        const heatmapResult = await analytics.getWritingHeatmap(
          userId, 
          days ? parseInt(days) : 30
        );
        return NextResponse.json(heatmapResult);

      case 'productivity_report':
        if (!userId || !timeframe) {
          return NextResponse.json(
            { error: 'userId and timeframe parameters required' },
            { status: 400 }
          );
        }
        const reportResult = await analytics.generateProductivityReport(userId, timeframe);
        return NextResponse.json(reportResult);

      case 'health':
        const healthResult = await analytics.healthCheck();
        return NextResponse.json(healthResult);

      default:
        return NextResponse.json({
          service: 'analytics-engine',
          version: '1.0.0',
          endpoints: [
            'POST /api/services/analytics - Track events and behavior',
            'GET /api/services/analytics?type=user_insights&userId=X - Get user insights',
            'GET /api/services/analytics?type=project_analytics&projectId=X - Get project analytics',
            'GET /api/services/analytics?type=writing_heatmap&userId=X&days=30 - Get writing heatmap',
            'GET /api/services/analytics?type=productivity_report&userId=X&timeframe=week - Get productivity report',
            'GET /api/services/analytics?type=health - Health check'
          ]
        });
    }
  } catch (error) {
    logger.error('Analytics API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}