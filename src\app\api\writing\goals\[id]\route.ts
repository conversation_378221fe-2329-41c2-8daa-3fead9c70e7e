import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'

const updateGoalSchema = z.object({
  target_words: z.number().int().positive().optional(),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).nullable().optional(),
  is_active: z.boolean().optional(),
})

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get goal with progress
    const { data: goal, error } = await supabase
      .from('writing_goals')
      .select('*')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Goal not found' }, { status: 404 })
      }
      logger.error('Error fetching goal:', error)
      return NextResponse.json({ error: 'Failed to fetch goal' }, { status: 500 })
    }

    // Get progress for this goal
    const { data: progress } = await supabase
      .from('writing_goal_progress')
      .select('*')
      .eq('goal_id', params.id)
      .order('date', { ascending: false })

    // Calculate statistics
    const totalWords = progress?.reduce((sum, p) => sum + p.words_written, 0) || 0
    const totalSessions = progress?.reduce((sum, p) => sum + p.sessions_count, 0) || 0
    const daysWithProgress = progress?.filter(p => p.words_written > 0).length || 0
    
    // Calculate current streak
    let currentStreak = 0
    const sortedProgress = progress?.sort((a, b) => b.date.localeCompare(a.date)) || []
    
    for (const p of sortedProgress) {
      if (p.words_written > 0) {
        if (currentStreak === 0 || isConsecutiveDay(p.date, sortedProgress[currentStreak - 1]?.date)) {
          currentStreak++
        } else {
          break
        }
      }
    }

    return NextResponse.json({ 
      goal,
      progress,
      statistics: {
        totalWords,
        totalSessions,
        daysWithProgress,
        currentStreak,
        averageWordsPerDay: daysWithProgress > 0 ? Math.round(totalWords / daysWithProgress) : 0,
        completionRate: calculateCompletionRate(goal, progress || []),
      }
    })
  } catch (error) {
    logger.error('Error in goal GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const updateData = updateGoalSchema.parse(body)

    // If activating this goal, deactivate others of the same type
    if (updateData.is_active === true) {
      const { data: existingGoal } = await supabase
        .from('writing_goals')
        .select('goal_type, project_id')
        .eq('id', params.id)
        .eq('user_id', user.id)
        .single()

      if (existingGoal) {
        const deactivateQuery = supabase
          .from('writing_goals')
          .update({ is_active: false })
          .eq('user_id', user.id)
          .eq('goal_type', existingGoal.goal_type)
          .eq('is_active', true)
          .neq('id', params.id)

        if (existingGoal.project_id) {
          deactivateQuery.eq('project_id', existingGoal.project_id)
        } else {
          deactivateQuery.is('project_id', null)
        }

        await deactivateQuery
      }
    }

    const { data: goal, error } = await supabase
      .from('writing_goals')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', params.id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Goal not found' }, { status: 404 })
      }
      logger.error('Error updating goal:', error)
      return NextResponse.json({ error: 'Failed to update goal' }, { status: 500 })
    }

    return NextResponse.json({ goal })
  } catch (error) {
    logger.error('Error in goal PATCH:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 })
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { error } = await supabase
      .from('writing_goals')
      .delete()
      .eq('id', params.id)
      .eq('user_id', user.id)

    if (error) {
      logger.error('Error deleting goal:', error)
      return NextResponse.json({ error: 'Failed to delete goal' }, { status: 500 })
    }

    return NextResponse.json({ success: true, message: 'Goal deleted' })
  } catch (error) {
    logger.error('Error in goal DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Helper functions
function isConsecutiveDay(date1: string, date2?: string): boolean {
  if (!date2) return false
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  const diffTime = Math.abs(d2.getTime() - d1.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays === 1
}

interface WritingGoal {
  id: string
  user_id: string
  goal_type: 'daily' | 'weekly' | 'monthly' | 'project'
  target_words: number
  project_id: string | null
  start_date: string
  end_date: string | null
  is_active: boolean
  created_at: string
  updated_at: string
}

interface WritingGoalProgress {
  id: string
  goal_id: string
  date: string
  words_written: number
  sessions_count: number
  created_at: string
}

function calculateCompletionRate(goal: WritingGoal, progress: WritingGoalProgress[]): number {
  if (!progress || progress.length === 0) return 0
  
  const startDate = new Date(goal.start_date)
  const endDate = goal.end_date ? new Date(goal.end_date) : new Date()
  const today = new Date()
  const effectiveEndDate = endDate < today ? endDate : today
  
  const totalDays = Math.ceil((effectiveEndDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
  const daysWithProgress = progress.filter(p => p.words_written >= goal.target_words).length
  
  return Math.round((daysWithProgress / totalDays) * 100)
}