import { GoogleGenerativeAI } from '@google/generative-ai'
import { logger } from '@/lib/services/logger'
import type { AIProvider, AIResponse, AIStreamResponse } from './types'

export class GeminiProvider implements AIProvider {
  private client: GoogleGenerativeAI | null = null
  private initialized = false

  constructor() {
    this.initialize()
  }

  private initialize() {
    const apiKey = process.env.GOOGLE_GEMINI_API_KEY
    if (apiKey) {
      this.client = new GoogleGenerativeAI(apiKey)
      this.initialized = true
    } else {
      logger.warn('Gemini API key not configured - fallback will not be available')
    }
  }

  isAvailable(): boolean {
    return this.initialized && this.client !== null
  }

  async generateCompletion(
    prompt: string,
    options: {
      model?: string
      temperature?: number
      maxTokens?: number
      systemPrompt?: string
    } = {}
  ): Promise<AIResponse> {
    if (!this.client) {
      throw new Error('Gemini provider not initialized')
    }

    try {
      const modelName = options.model || 'gemini-pro'
      const model = this.client.getGenerativeModel({ model: modelName })

      // Combine system prompt with user prompt
      const fullPrompt = options.systemPrompt 
        ? `${options.systemPrompt}\n\n${prompt}`
        : prompt

      const result = await model.generateContent({
        contents: [{ role: 'user', parts: [{ text: fullPrompt }] }],
        generationConfig: {
          temperature: options.temperature || 0.7,
          maxOutputTokens: options.maxTokens || 4000,
        },
      })

      const response = await result.response
      const text = response.text()

      return {
        content: text,
        model: modelName,
        usage: {
          promptTokens: 0, // Gemini doesn't provide token counts in the same way
          completionTokens: 0,
          totalTokens: 0,
        },
      }
    } catch (error) {
      logger.error('Gemini generation error:', error)
      throw error
    }
  }

  async *generateStream(
    prompt: string,
    options: {
      model?: string
      temperature?: number
      maxTokens?: number
      systemPrompt?: string
    } = {}
  ): AIStreamResponse {
    if (!this.client) {
      throw new Error('Gemini provider not initialized')
    }

    try {
      const modelName = options.model || 'gemini-pro'
      const model = this.client.getGenerativeModel({ model: modelName })

      // Combine system prompt with user prompt
      const fullPrompt = options.systemPrompt 
        ? `${options.systemPrompt}\n\n${prompt}`
        : prompt

      const result = await model.generateContentStream({
        contents: [{ role: 'user', parts: [{ text: fullPrompt }] }],
        generationConfig: {
          temperature: options.temperature || 0.7,
          maxOutputTokens: options.maxTokens || 4000,
        },
      })

      for await (const chunk of result.stream) {
        const text = chunk.text()
        if (text) {
          yield { content: text }
        }
      }
    } catch (error) {
      logger.error('Gemini streaming error:', error)
      throw error
    }
  }

  // Map OpenAI model names to Gemini equivalents
  getEquivalentModel(openAIModel: string): string {
    const modelMap: Record<string, string> = {
      'gpt-4.1': 'gemini-1.5-pro',
      'gpt-4.1-mini': 'gemini-1.5-flash',
      'gpt-4o': 'gemini-1.5-pro',
      'gpt-4o-mini': 'gemini-1.5-flash',
      'gpt-4': 'gemini-pro',
      'gpt-3.5-turbo': 'gemini-pro',
    }

    return modelMap[openAIModel] || 'gemini-pro'
  }
}