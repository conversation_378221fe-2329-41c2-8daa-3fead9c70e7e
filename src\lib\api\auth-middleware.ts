import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger'

export interface AuthUser {
  id: string
  email: string
  role?: string
}

/**
 * Require authentication for API routes
 * Returns user if authenticated, or error response if not
 */
export async function requireAuth(request: NextRequest): Promise<AuthUser | NextResponse> {
  try {
    const supabase = createClient()
    
    // Get user from Supabase auth
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      logger.warn('Authentication failed', {
        error: error?.message,
        url: request.url,
        method: request.method
      })
      
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }
    
    // Get user role from database if needed
    const { data: profile } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()
    
    logger.info('API authenticated', {
      userId: user.id,
      url: request.url,
      method: request.method
    })
    
    return {
      id: user.id,
      email: user.email!,
      role: profile?.role
    }
  } catch (error) {
    logger.error('Auth middleware error', error)
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Require admin role for API routes
 */
export async function requireAdmin(request: NextRequest): Promise<AuthUser | NextResponse> {
  const authResult = await requireAuth(request)
  
  // If auth failed, return the error response
  if (authResult instanceof NextResponse) {
    return authResult
  }
  
  // Check if user has admin role
  if (authResult.role !== 'admin') {
    logger.warn('Admin access denied', {
      userId: authResult.id,
      role: authResult.role,
      url: request.url
    })
    
    return NextResponse.json(
      { error: 'Forbidden', message: 'Admin access required' },
      { status: 403 }
    )
  }
  
  return authResult
}

/**
 * Verify user has access to a specific project
 */
export async function requireProjectAccess(
  request: NextRequest,
  projectId: string,
  requiredRole: 'viewer' | 'editor' | 'owner' = 'viewer'
): Promise<AuthUser | NextResponse> {
  const authResult = await requireAuth(request)
  
  // If auth failed, return the error response
  if (authResult instanceof NextResponse) {
    return authResult
  }
  
  const supabase = createClient()
  
  // Check if user owns the project
  const { data: project } = await supabase
    .from('projects')
    .select('user_id')
    .eq('id', projectId)
    .single()
  
  if (project?.user_id === authResult.id) {
    return authResult // Owner has full access
  }
  
  // Check collaboration access
  const { data: collaborator } = await supabase
    .from('project_collaborators')
    .select('role, status')
    .eq('project_id', projectId)
    .eq('user_id', authResult.id)
    .eq('status', 'active')
    .single()
  
  if (!collaborator) {
    logger.warn('Project access denied', {
      userId: authResult.id,
      projectId,
      url: request.url
    })
    
    return NextResponse.json(
      { error: 'Forbidden', message: 'Access to this project is denied' },
      { status: 403 }
    )
  }
  
  // Check role hierarchy
  const roleHierarchy = {
    viewer: 1,
    editor: 2,
    owner: 3
  }
  
  if (roleHierarchy[collaborator.role] < roleHierarchy[requiredRole]) {
    return NextResponse.json(
      { error: 'Forbidden', message: `${requiredRole} access required` },
      { status: 403 }
    )
  }
  
  return authResult
}

/**
 * Optional authentication - doesn't fail if user is not authenticated
 */
export async function optionalAuth(request: NextRequest): Promise<AuthUser | null> {
  try {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return null
    }
    
    return {
      id: user.id,
      email: user.email!
    }
  } catch (error) {
    logger.error('Optional auth error', error)
    return null
  }
}

/**
 * Extract project ID from URL path
 */
export function extractProjectId(pathname: string): string | null {
  const match = pathname.match(/\/projects\/([a-zA-Z0-9-]+)/)
  return match ? match[1] : null
}

/**
 * Rate limiting helper
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  identifier: string,
  limit: number = 60,
  windowMs: number = 60000
): boolean {
  const now = Date.now()
  const userLimit = rateLimitMap.get(identifier)
  
  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(identifier, {
      count: 1,
      resetTime: now + windowMs
    })
    return true
  }
  
  if (userLimit.count >= limit) {
    return false
  }
  
  userLimit.count++
  return true
}

/**
 * Apply rate limiting to authenticated requests
 */
export async function withRateLimit(
  request: NextRequest,
  limit: number = 60,
  windowMs: number = 60000
): Promise<AuthUser | NextResponse> {
  const authResult = await requireAuth(request)
  
  if (authResult instanceof NextResponse) {
    return authResult
  }
  
  if (!checkRateLimit(authResult.id, limit, windowMs)) {
    logger.warn('Rate limit exceeded', {
      userId: authResult.id,
      url: request.url
    })
    
    return NextResponse.json(
      { error: 'Too many requests', message: 'Rate limit exceeded' },
      { status: 429 }
    )
  }
  
  return authResult
}