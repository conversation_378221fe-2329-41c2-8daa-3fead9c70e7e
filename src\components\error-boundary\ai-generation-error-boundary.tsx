'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertTriangle, RefreshCw, Sparkles, CreditCard, Zap } from 'lucide-react'
import { logger } from '@/lib/services/logger'
import Link from 'next/link'

interface Props {
  children: React.ReactNode
  onRetry?: () => void
  projectId?: string
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  isRateLimitError: boolean
  isQuotaError: boolean
  isModelError: boolean
}

export class AIGenerationErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      isRateLimitError: false,
      isQuotaError: false,
      isModelError: false
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorMessage = error.message.toLowerCase()
    
    return { 
      hasError: true, 
      error,
      isRateLimitError: errorMessage.includes('rate limit') || errorMessage.includes('too many'),
      isQuotaError: errorMessage.includes('quota') || errorMessage.includes('limit exceeded'),
      isModelError: errorMessage.includes('model') || errorMessage.includes('tier')
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    logger.error('AI Generation error boundary caught error', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      projectId: this.props.projectId
    })

    this.setState({
      errorInfo
    })
  }

  handleReset = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      isRateLimitError: false,
      isQuotaError: false,
      isModelError: false
    })
  }

  handleRetry = () => {
    this.handleReset()
    this.props.onRetry?.()
  }

  render() {
    if (this.state.hasError) {
      const { isRateLimitError, isQuotaError, isModelError } = this.state

      return (
        <div className="min-h-[300px] flex items-center justify-center p-4">
          <Card className="max-w-lg w-full">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-amber-500/10 flex items-center justify-center">
                {isQuotaError ? (
                  <CreditCard className="h-6 w-6 text-amber-500" />
                ) : isRateLimitError ? (
                  <Zap className="h-6 w-6 text-amber-500" />
                ) : (
                  <Sparkles className="h-6 w-6 text-amber-500" />
                )}
              </div>
              <CardTitle>
                {isRateLimitError 
                  ? "Slow Down There!"
                  : isQuotaError
                  ? "Usage Limit Reached"
                  : isModelError
                  ? "Model Access Restricted"
                  : "AI Generation Error"}
              </CardTitle>
              <CardDescription>
                {isRateLimitError 
                  ? "You're making requests too quickly. Please wait a moment."
                  : isQuotaError
                  ? "You've reached your AI generation limit for this period."
                  : isModelError
                  ? "This AI model requires a higher subscription tier."
                  : "Something went wrong during AI generation."}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isRateLimitError && (
                <Alert>
                  <AlertTitle>Rate Limit Information</AlertTitle>
                  <AlertDescription>
                    Our AI has request limits to ensure quality service for everyone. 
                    Please wait 60 seconds before trying again.
                  </AlertDescription>
                </Alert>
              )}

              {isQuotaError && (
                <Alert>
                  <AlertTitle>Upgrade for More</AlertTitle>
                  <AlertDescription>
                    Unlock unlimited AI generations and advanced models with our Professional or Studio plans.
                  </AlertDescription>
                </Alert>
              )}

              {isModelError && (
                <Alert>
                  <AlertTitle>Advanced Model Required</AlertTitle>
                  <AlertDescription>
                    This feature uses our most advanced AI models. Upgrade to access GPT-4.1 and other premium models.
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                {(isRateLimitError || (!isQuotaError && !isModelError)) && (
                  <Button 
                    onClick={this.handleRetry}
                    className="w-full"
                    variant="default"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    {isRateLimitError ? "Try Again (Wait 60s)" : "Try Again"}
                  </Button>
                )}

                {(isQuotaError || isModelError) && (
                  <Link href="/pricing" className="block">
                    <Button className="w-full" variant="default">
                      <Sparkles className="h-4 w-4 mr-2" />
                      View Upgrade Options
                    </Button>
                  </Link>
                )}

                <Button 
                  onClick={() => window.location.reload()}
                  className="w-full"
                  variant="outline"
                >
                  Refresh Page
                </Button>
              </div>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-4">
                  <summary className="text-sm text-muted-foreground cursor-pointer">
                    Error Details
                  </summary>
                  <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                    {this.state.error.message}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}