import { createClient } from '@/lib/supabase/client'
import type { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'
import { logger } from './logger'
import { hasRealTimeCollaboration } from '../subscription'
import type { UserSubscription } from '../subscription'

// Database table types
interface CollaborationParticipant {
  id: string
  session_id: string
  user_id: string
  user_name: string
  user_email: string
  role: 'viewer' | 'editor' | 'owner'
  status: 'online' | 'offline'
  color: string
  last_active: string
  joined_at: string
}

interface CollaborationChange {
  id: string
  session_id: string
  user_id: string
  type: 'insert' | 'delete' | 'format'
  range: {
    startLine: number
    startColumn: number
    endLine: number
    endColumn: number
  }
  text?: string
  timestamp: string
}

export interface CollaborationUser {
  id: string
  name: string
  email: string
  color: string
  cursor?: {
    line: number
    column: number
  }
  selection?: {
    startLine: number
    startColumn: number
    endLine: number
    endColumn: number
  }
}

export interface CollaborationSession {
  id: string
  projectId: string
  documentId: string
  users: Map<string, CollaborationUser>
  isActive: boolean
  createdAt: Date
}

export interface CollaborationChange {
  userId: string
  sessionId: string
  type: 'insert' | 'delete' | 'replace'
  range: {
    startLine: number
    startColumn: number
    endLine: number
    endColumn: number
  }
  text: string
  timestamp: number
}

export interface CursorPosition {
  userId: string
  line: number
  column: number
  timestamp: number
}

export interface SelectionRange {
  userId: string
  startLine: number
  startColumn: number
  endLine: number
  endColumn: number
  timestamp: number
}

type CollaborationEventType = 
  | 'user.joined'
  | 'user.left'
  | 'cursor.moved'
  | 'selection.changed'
  | 'content.changed'
  | 'session.created'
  | 'session.ended'

interface CollaborationEvent {
  type: CollaborationEventType
  sessionId: string
  userId: string
  data: any
  timestamp: number
}

export class CollaborationServiceRealtime {
  private static instance: CollaborationServiceRealtime
  private supabase = createClient()
  private channels: Map<string, RealtimeChannel> = new Map()
  private sessions: Map<string, CollaborationSession> = new Map()
  private listeners: Map<string, Set<(event: CollaborationEvent) => void>> = new Map()
  private userColors: Map<string, string> = new Map()
  
  private constructor() {}
  
  static getInstance(): CollaborationServiceRealtime {
    if (!CollaborationServiceRealtime.instance) {
      CollaborationServiceRealtime.instance = new CollaborationServiceRealtime()
    }
    return CollaborationServiceRealtime.instance
  }

  /**
   * Check if real-time collaboration is available for the user
   */
  canUseRealTimeCollaboration(subscription: UserSubscription | null): boolean {
    if (!subscription) return false
    const tier = subscription.tierId
    // Professional tier gets 2 collaborators, Studio gets 5
    return tier === 'professional' || tier === 'studio'
  }

  /**
   * Get maximum collaborators allowed for subscription
   */
  getMaxCollaborators(subscription: UserSubscription | null): number {
    if (!subscription) return 0
    switch (subscription.tierId) {
      case 'professional': return 2
      case 'studio': return 5
      default: return 0
    }
  }

  /**
   * Connect to collaboration session via Supabase Realtime
   */
  async connect(
    sessionId: string,
    userId: string,
    userName: string,
    userEmail: string,
    subscription: UserSubscription | null
  ): Promise<void> {
    // Check subscription
    if (!this.canUseRealTimeCollaboration(subscription)) {
      throw new Error('Real-time collaboration requires Professional or Studio tier subscription')
    }
    
    try {
      logger.info('Connecting to collaboration session', { sessionId, userId })
      
      // Create or join session in database
      const { error: sessionError } = await this.supabase
        .from('collaboration_sessions')
        .upsert({
          id: sessionId,
          project_id: sessionId.split(':')[1], // Extract project ID
          owner_id: userId,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'id'
        })

      if (sessionError) throw sessionError

      // Add user as participant
      const userColor = this.generateUserColor(userId)
      this.userColors.set(userId, userColor)

      const { error: participantError } = await this.supabase
        .from('collaboration_participants')
        .upsert({
          session_id: sessionId,
          user_id: userId,
          role: 'editor', // Default role
          status: 'online',
          cursor_position: null,
          joined_at: new Date().toISOString(),
          last_seen: new Date().toISOString()
        }, {
          onConflict: 'session_id,user_id'
        })

      if (participantError) throw participantError

      // Set up Realtime channel
      const channel = this.supabase.channel(`collaboration:${sessionId}`)
      
      // Subscribe to participant changes
      channel
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'collaboration_participants',
          filter: `session_id=eq.${sessionId}`
        }, (payload) => this.handleParticipantChange(sessionId, payload))
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'collaboration_changes',
          filter: `session_id=eq.${sessionId}`
        }, (payload) => this.handleContentChange(sessionId, payload))
        .on('broadcast', {
          event: 'cursor'
        }, (payload) => this.handleCursorUpdate(sessionId, payload))
        .on('broadcast', {
          event: 'selection'
        }, (payload) => this.handleSelectionUpdate(sessionId, payload))
        .on('presence', {
          event: 'sync'
        }, () => this.handlePresenceSync(sessionId, channel))
        .subscribe(async (status) => {
          if (status === 'SUBSCRIBED') {
            logger.info('Subscribed to collaboration channel', { sessionId })
            
            // Track presence
            await channel.track({
              user_id: userId,
              user_name: userName,
              user_email: userEmail,
              user_color: userColor,
              online_at: new Date().toISOString()
            })
            
            // Create local session
            const session: CollaborationSession = {
              id: sessionId,
              projectId: sessionId.split(':')[1],
              documentId: sessionId.split(':')[3] || 'default',
              users: new Map(),
              isActive: true,
              createdAt: new Date()
            }
            
            // Add current user
            session.users.set(userId, {
              id: userId,
              name: userName,
              email: userEmail,
              color: userColor
            })
            
            this.sessions.set(sessionId, session)
            this.channels.set(sessionId, channel)
            
            // Notify listeners
            this.notifyListeners(sessionId, {
              type: 'user.joined',
              sessionId,
              userId,
              data: {
                id: userId,
                name: userName,
                email: userEmail,
                color: userColor
              },
              timestamp: Date.now()
            })
          }
        })
      
    } catch (error) {
      logger.error('Failed to connect to collaboration session', error)
      throw error
    }
  }

  /**
   * Disconnect from collaboration session
   */
  async disconnect(sessionId: string, userId: string): Promise<void> {
    try {
      // Update participant status to offline
      await this.supabase
        .from('collaboration_participants')
        .update({
          status: 'offline',
          last_seen: new Date().toISOString()
        })
        .eq('session_id', sessionId)
        .eq('user_id', userId)

      // Unsubscribe from channel
      const channel = this.channels.get(sessionId)
      if (channel) {
        await channel.unsubscribe()
        this.channels.delete(sessionId)
      }
      
      // Remove session
      this.sessions.delete(sessionId)
      
      // Notify listeners
      this.notifyListeners(sessionId, {
        type: 'user.left',
        sessionId,
        userId,
        data: null,
        timestamp: Date.now()
      })
      
      logger.info('Disconnected from collaboration session', { sessionId, userId })
    } catch (error) {
      logger.error('Error disconnecting from session', error)
    }
  }

  /**
   * Send cursor position update
   */
  async sendCursorPosition(sessionId: string, position: Omit<CursorPosition, 'timestamp'>): Promise<void> {
    const channel = this.channels.get(sessionId)
    if (!channel) return

    // Broadcast cursor position
    await channel.send({
      type: 'broadcast',
      event: 'cursor',
      payload: {
        userId: position.userId,
        line: position.line,
        column: position.column,
        timestamp: Date.now()
      }
    })

    // Update in database for persistence
    await this.supabase
      .from('collaboration_participants')
      .update({
        cursor_position: {
          line: position.line,
          column: position.column
        },
        last_seen: new Date().toISOString()
      })
      .eq('session_id', sessionId)
      .eq('user_id', position.userId)
  }

  /**
   * Send selection range update
   */
  async sendSelectionRange(sessionId: string, selection: Omit<SelectionRange, 'timestamp'>): Promise<void> {
    const channel = this.channels.get(sessionId)
    if (!channel) return

    // Broadcast selection
    await channel.send({
      type: 'broadcast',
      event: 'selection',
      payload: {
        userId: selection.userId,
        startLine: selection.startLine,
        startColumn: selection.startColumn,
        endLine: selection.endLine,
        endColumn: selection.endColumn,
        timestamp: Date.now()
      }
    })
  }

  /**
   * Send content change
   */
  async sendContentChange(sessionId: string, change: Omit<CollaborationChange, 'timestamp'>): Promise<void> {
    try {
      // Get current document version
      const { data: session } = await this.supabase
        .from('collaboration_sessions')
        .select('document_version')
        .eq('id', sessionId)
        .single()

      const version = (session?.document_version || 0) + 1

      // Record change in database
      await this.supabase
        .from('collaboration_changes')
        .insert({
          id: `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          session_id: sessionId,
          user_id: change.userId,
          type: change.type,
          position: {
            line: change.range.startLine,
            column: change.range.startColumn
          },
          content: change.text,
          length: change.text.length,
          version,
          created_at: new Date().toISOString()
        })

      // Update document version
      await this.supabase
        .from('collaboration_sessions')
        .update({
          document_version: version,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId)

    } catch (error) {
      logger.error('Failed to send content change', error)
      throw error
    }
  }

  /**
   * Subscribe to collaboration events
   */
  subscribe(
    sessionId: string,
    callback: (event: CollaborationEvent) => void
  ): () => void {
    if (!this.listeners.has(sessionId)) {
      this.listeners.set(sessionId, new Set())
    }
    
    this.listeners.get(sessionId)!.add(callback)
    
    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(sessionId)
      if (listeners) {
        listeners.delete(callback)
        if (listeners.size === 0) {
          this.listeners.delete(sessionId)
        }
      }
    }
  }

  /**
   * Get active session
   */
  getSession(sessionId: string): CollaborationSession | undefined {
    return this.sessions.get(sessionId)
  }

  /**
   * Get all active users in a session
   */
  getActiveUsers(sessionId: string): CollaborationUser[] {
    const session = this.sessions.get(sessionId)
    return session ? Array.from(session.users.values()) : []
  }

  // Private methods

  private handleParticipantChange(sessionId: string, payload: RealtimePostgresChangesPayload<CollaborationParticipant>): void {
    const session = this.sessions.get(sessionId)
    if (!session) return

    if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
      const participant = payload.new
      if (participant.status === 'online' && participant.user_id !== session.users.get(participant.user_id)?.id) {
        // New user joined
        this.notifyListeners(sessionId, {
          type: 'user.joined',
          sessionId,
          userId: participant.user_id,
          data: {
            id: participant.user_id,
            role: participant.role,
            status: participant.status
          },
          timestamp: Date.now()
        })
      } else if (participant.status === 'offline') {
        // User left
        session.users.delete(participant.user_id)
        this.notifyListeners(sessionId, {
          type: 'user.left',
          sessionId,
          userId: participant.user_id,
          data: null,
          timestamp: Date.now()
        })
      }
    }
  }

  private handleContentChange(sessionId: string, payload: RealtimePostgresChangesPayload<CollaborationChange>): void {
    if (payload.eventType === 'INSERT') {
      const change = payload.new
      this.notifyListeners(sessionId, {
        type: 'content.changed',
        sessionId,
        userId: change.user_id,
        data: {
          type: change.type,
          range: {
            startLine: change.position.line,
            startColumn: change.position.column,
            endLine: change.position.line,
            endColumn: change.position.column + (change.length || 0)
          },
          text: change.content || '',
          version: change.version
        },
        timestamp: Date.now()
      })
    }
  }

  private handleCursorUpdate(sessionId: string, payload: any): void {
    const session = this.sessions.get(sessionId)
    if (!session) return

    const { userId, line, column } = payload.payload
    const user = session.users.get(userId)
    if (user) {
      user.cursor = { line, column }
    }

    this.notifyListeners(sessionId, {
      type: 'cursor.moved',
      sessionId,
      userId,
      data: { line, column },
      timestamp: Date.now()
    })
  }

  private handleSelectionUpdate(sessionId: string, payload: any): void {
    const session = this.sessions.get(sessionId)
    if (!session) return

    const { userId, startLine, startColumn, endLine, endColumn } = payload.payload
    const user = session.users.get(userId)
    if (user) {
      user.selection = { startLine, startColumn, endLine, endColumn }
    }

    this.notifyListeners(sessionId, {
      type: 'selection.changed',
      sessionId,
      userId,
      data: { startLine, startColumn, endLine, endColumn },
      timestamp: Date.now()
    })
  }

  private handlePresenceSync(sessionId: string, channel: RealtimeChannel): void {
    const session = this.sessions.get(sessionId)
    if (!session) return

    const presence = channel.presenceState()
    
    // Update session users based on presence
    session.users.clear()
    
    Object.entries(presence).forEach(([key, presences]) => {
      if (Array.isArray(presences)) {
        presences.forEach((p: any) => {
          const userId = p.user_id
          const userColor = this.userColors.get(userId) || this.generateUserColor(userId)
          
          session.users.set(userId, {
            id: userId,
            name: p.user_name || 'Unknown User',
            email: p.user_email || '<EMAIL>',
            color: p.user_color || userColor
          })
        })
      }
    })
  }

  private notifyListeners(sessionId: string, event: CollaborationEvent): void {
    const listeners = this.listeners.get(sessionId)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(event)
        } catch (error) {
          logger.error('Error in collaboration event listener', error)
        }
      })
    }
  }

  private generateUserColor(userId: string): string {
    const colors = [
      '#3B82F6', // Blue
      '#EF4444', // Red
      '#10B981', // Green
      '#F59E0B', // Orange
      '#8B5CF6', // Purple
      '#EC4899', // Pink
      '#14B8A6', // Teal
      '#F97316', // Orange
      '#6366F1', // Indigo
      '#84CC16'  // Lime
    ]
    
    // Generate consistent color based on user ID
    const hash = userId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
    return colors[hash % colors.length]
  }
}

export const collaborationServiceRealtime = CollaborationServiceRealtime.getInstance()