import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { authenticateUser } from '@/lib/auth/server'
import { ServiceManager } from '@/lib/services/service-manager'
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    // Authentication
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const body = await request.json()
    const { sessionId, change } = body

    if (!sessionId || !change) {
      return NextResponse.json(
        { error: 'Session ID and change are required' },
        { status: 400 }
      )
    }

    // Validate change structure
    if (!change.type || !change.position) {
      return NextResponse.json(
        { error: 'Invalid change format' },
        { status: 400 }
      )
    }

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationHub = await serviceManager.getCollaborationHub()
    if (!collaborationHub) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    const result = await collaborationHub.applyChange(
      sessionId,
      authResult.user.id,
      change
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to apply change' },
        { status: 400 }
      )
    }

    return NextResponse.json(result.data)
  } catch (error) {
    logger.error('Collaboration change error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}