import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import type { DevelopmentGridData, ChapterDevelopment } from '@/lib/types/character-development';
import { DEVELOPMENT_DIMENSIONS } from '@/lib/types/character-development';
import { logger } from '@/lib/services/logger'

interface Character {
  id: string;
  name: string;
  role?: string;
  personality_traits?: {
    beliefs?: string[];
    skills?: string[];
    desires?: string[];
    fears?: string[];
    [key: string]: unknown;
  };
  relationships?: Record<string, unknown>;
  [key: string]: unknown;
}

interface Chapter {
  id: string;
  chapter_number: number;
  content?: string;
  outline?: string;
}

interface EmotionalAnalysis {
  change: number;
  events: string[];
}

interface DimensionAnalysis {
  change: number;
  events: string[];
}

interface PreviousState {
  emotionalMaturity: number;
  beliefs: string[];
  skills: string[];
  relationships: Record<string, unknown>;
  goals: string[];
  internalConflicts: string[];
  agency: number;
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const characterId = searchParams.get('characterId');
    const projectId = searchParams.get('projectId');

    if (!characterId || !projectId) {
      return NextResponse.json(
        { error: 'Character ID and Project ID are required' },
        { status: 400 }
      );
    }

    // Verify user has access to this project
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', projectId)
      .single();

    if (!project || project.user_id !== user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Fetch character data
    const { data: character, error: charError } = await supabase
      .from('characters')
      .select('*')
      .eq('id', characterId)
      .eq('project_id', projectId)
      .single();

    if (charError || !character) {
      return NextResponse.json(
        { error: 'Character not found' },
        { status: 404 }
      );
    }

    // Fetch chapters for this project
    const { data: chapters, error: chaptersError } = await supabase
      .from('chapters')
      .select('id, chapter_number, content, outline')
      .eq('project_id', projectId)
      .order('chapter_number', { ascending: true });

    if (chaptersError) {
      throw new Error('Failed to fetch chapters');
    }

    // Analyze character development across chapters
    const chapterDevelopments = await analyzeCharacterDevelopment(
      character,
      chapters || []
    );

    const gridData: DevelopmentGridData = {
      characterId: character.id,
      characterName: character.name,
      dimensions: DEVELOPMENT_DIMENSIONS,
      chapters: chapterDevelopments
    };

    return NextResponse.json(gridData);
  } catch (error) {
    logger.error('Error fetching character development grid:', error);
    return NextResponse.json(
      { error: 'Failed to fetch character development data' },
      { status: 500 }
    );
  }
}

// Helper function to analyze character development
async function analyzeCharacterDevelopment(
  character: Character,
  chapters: Chapter[]
): Promise<ChapterDevelopment[]> {
  const developments = [];
  
  // Initialize previous state
  let previousState = {
    emotionalMaturity: 50,
    beliefs: character.personality_traits?.beliefs || [],
    skills: character.personality_traits?.skills || [],
    relationships: character.relationships || {},
    goals: character.personality_traits?.desires || [],
    internalConflicts: character.personality_traits?.fears || [],
    agency: 50
  };

  for (const chapter of chapters) {
    const chapterDev: ChapterDevelopment = {
      chapter: chapter.chapter_number,
      dimensions: {}
    };

    // Analyze each dimension
    // This is a simplified analysis - in production, you'd use AI to analyze the actual content
    const content = chapter.content || '';
    const outline = chapter.outline || '';
    const characterMentions = countCharacterMentions(content, character.name);

    // Emotional Growth
    const emotionalEvents = analyzeEmotionalEvents(content, character.name);
    const emotionalGrowth = calculateEmotionalGrowth(emotionalEvents, previousState.emotionalMaturity);
    chapterDev.dimensions['emotional-growth'] = {
      value: emotionalGrowth.change,
      events: emotionalGrowth.events,
      type: getChangeType(emotionalGrowth.change)
    };

    // Belief System
    const beliefChanges = analyzeBeliefChanges(content, outline, character.name);
    chapterDev.dimensions['belief-system'] = {
      value: beliefChanges.change,
      events: beliefChanges.events,
      type: getChangeType(beliefChanges.change)
    };

    // Skill Progression
    const skillProgress = analyzeSkillProgression(content, character.name);
    chapterDev.dimensions['skill-progression'] = {
      value: skillProgress.change,
      events: skillProgress.events,
      type: getChangeType(skillProgress.change)
    };

    // Relationship Dynamics
    const relationshipChanges = analyzeRelationshipChanges(content, character.name, character.relationships || {});
    chapterDev.dimensions['relationships'] = {
      value: relationshipChanges.change,
      events: relationshipChanges.events,
      type: getChangeType(relationshipChanges.change)
    };

    // Goal Alignment
    const goalProgress = analyzeGoalAlignment(content, outline, character.personality_traits?.desires || []);
    chapterDev.dimensions['goal-alignment'] = {
      value: goalProgress.change,
      events: goalProgress.events,
      type: getChangeType(goalProgress.change)
    };

    // Internal Conflict
    const conflictResolution = analyzeInternalConflict(content, character.personality_traits?.fears || []);
    chapterDev.dimensions['internal-conflict'] = {
      value: conflictResolution.change,
      events: conflictResolution.events,
      type: getChangeType(conflictResolution.change)
    };

    // External Agency
    const agencyChange = analyzeAgency(content, character.name, characterMentions);
    chapterDev.dimensions['external-agency'] = {
      value: agencyChange.change,
      events: agencyChange.events,
      type: getChangeType(agencyChange.change)
    };

    developments.push(chapterDev);

    // Update previous state for next iteration
    previousState = updatePreviousState(previousState, chapterDev);
  }

  return developments;
}

// Helper functions for analysis
function countCharacterMentions(content: string, characterName: string): number {
  const regex = new RegExp(characterName, 'gi');
  const matches = content.match(regex);
  return matches ? matches.length : 0;
}

function analyzeEmotionalEvents(content: string, characterName: string) {
  // Simplified emotion analysis - in production, use AI
  const emotionalKeywords = {
    positive: ['smiled', 'laughed', 'joy', 'happy', 'excited', 'confident'],
    negative: ['cried', 'angry', 'sad', 'frustrated', 'defeated', 'afraid'],
    growth: ['realized', 'understood', 'learned', 'accepted', 'overcame']
  };

  const events: string[] = [];
  let score = 0;

  // Check for emotional keywords near character mentions
  const sentences = content.split(/[.!?]+/);
  sentences.forEach(sentence => {
    if (sentence.includes(characterName)) {
      emotionalKeywords.positive.forEach(keyword => {
        if (sentence.toLowerCase().includes(keyword)) {
          score += 5;
          events.push(`Showed positive emotion: ${keyword}`);
        }
      });
      emotionalKeywords.growth.forEach(keyword => {
        if (sentence.toLowerCase().includes(keyword)) {
          score += 10;
          events.push(`Emotional growth: ${keyword}`);
        }
      });
      emotionalKeywords.negative.forEach(keyword => {
        if (sentence.toLowerCase().includes(keyword)) {
          score -= 3; // Negative emotions can lead to growth
          events.push(`Experienced: ${keyword}`);
        }
      });
    }
  });

  return { change: Math.min(Math.max(score, -50), 50), events: events.slice(0, 3) };
}

function calculateEmotionalGrowth(emotionalEvents: EmotionalAnalysis, _previousMaturity: number): EmotionalAnalysis {
  return emotionalEvents;
}

function analyzeBeliefChanges(content: string, outline: string, characterName: string) {
  const beliefKeywords = ['believed', 'thought', 'realized', 'questioned', 'doubted', 'faith', 'trust'];
  const events: string[] = [];
  let change = 0;

  beliefKeywords.forEach(keyword => {
    if ((content + outline).toLowerCase().includes(keyword) && 
        (content + outline).includes(characterName)) {
      change += 8;
      events.push(`Belief system evolved: ${keyword}`);
    }
  });

  return { change: Math.min(change, 40), events: events.slice(0, 2) };
}

function analyzeSkillProgression(content: string, characterName: string) {
  const skillKeywords = ['learned', 'mastered', 'practiced', 'failed', 'improved', 'trained'];
  const events: string[] = [];
  let change = 0;

  skillKeywords.forEach(keyword => {
    if (content.toLowerCase().includes(keyword) && content.includes(characterName)) {
      change += keyword === 'failed' ? -5 : 10;
      events.push(`Skill development: ${keyword}`);
    }
  });

  return { change: Math.min(Math.max(change, -30), 50), events: events.slice(0, 2) };
}

function analyzeRelationshipChanges(content: string, _characterName: string, _relationships: Record<string, unknown>): DimensionAnalysis {
  const relationshipKeywords = {
    positive: ['befriended', 'allied', 'trusted', 'loved', 'helped', 'saved'],
    negative: ['betrayed', 'argued', 'confronted', 'abandoned', 'hurt']
  };
  const events: string[] = [];
  let change = 0;

  Object.values(relationshipKeywords.positive).forEach(keyword => {
    if (content.toLowerCase().includes(keyword)) {
      change += 8;
      events.push(`Relationship strengthened: ${keyword}`);
    }
  });

  Object.values(relationshipKeywords.negative).forEach(keyword => {
    if (content.toLowerCase().includes(keyword)) {
      change -= 5;
      events.push(`Relationship challenged: ${keyword}`);
    }
  });

  return { change: Math.min(Math.max(change, -40), 40), events: events.slice(0, 2) };
}

function analyzeGoalAlignment(content: string, outline: string, goals: string[]) {
  const events: string[] = [];
  let change = 0;

  if (goals && goals.length > 0) {
    goals.forEach(goal => {
      if ((content + outline).toLowerCase().includes(goal.toLowerCase())) {
        change += 15;
        events.push(`Progress toward: ${goal}`);
      }
    });
  }

  // Check for goal-related keywords
  if (content.includes('achieved') || content.includes('accomplished')) {
    change += 20;
    events.push('Goal milestone reached');
  }

  return { change: Math.min(change, 60), events };
}

function analyzeInternalConflict(content: string, _fears: string[]) {
  const conflictKeywords = ['struggled', 'conflicted', 'torn', 'resolved', 'overcame', 'accepted'];
  const events: string[] = [];
  let change = 0;

  conflictKeywords.forEach(keyword => {
    if (content.toLowerCase().includes(keyword)) {
      change += keyword.includes('resolved') || keyword.includes('overcame') ? 20 : 5;
      events.push(`Internal conflict: ${keyword}`);
    }
  });

  return { change: Math.min(change, 50), events: events.slice(0, 2) };
}

function analyzeAgency(content: string, characterName: string, mentions: number) {
  const actionKeywords = ['decided', 'chose', 'acted', 'led', 'commanded', 'initiated'];
  const events: string[] = [];
  let change = 0;

  actionKeywords.forEach(keyword => {
    if (content.toLowerCase().includes(keyword) && content.includes(characterName)) {
      change += 10;
      events.push(`Showed agency: ${keyword}`);
    }
  });

  // High mention count suggests active role
  if (mentions > 20) {
    change += 15;
    events.push('Central role in chapter');
  }

  return { change: Math.min(change, 60), events: events.slice(0, 2) };
}

function getChangeType(value: number): 'growth' | 'stagnant' | 'regression' | 'breakthrough' {
  if (value >= 40) return 'breakthrough';
  if (value > 0) return 'growth';
  if (value < 0) return 'regression';
  return 'stagnant';
}

function updatePreviousState(previousState: PreviousState, chapterDev: ChapterDevelopment): PreviousState {
  // Update state based on changes
  const newState = { ...previousState };
  
  Object.entries(chapterDev.dimensions).forEach(([dimension, data]) => {
    if (dimension === 'emotional-growth' && data.value > 0) {
      newState.emotionalMaturity = Math.min(100, newState.emotionalMaturity + data.value / 2);
    }
    if (dimension === 'external-agency' && data.value > 0) {
      newState.agency = Math.min(100, newState.agency + data.value / 2);
    }
  });

  return newState;
}