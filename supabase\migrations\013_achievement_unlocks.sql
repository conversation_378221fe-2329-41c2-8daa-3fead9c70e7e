-- Migration: Achievement unlocks tracking
-- Dependencies: 001_enhanced_schema.sql (users), 20250119_create_achievements.sql
-- Rollback: DROP TABLE IF EXISTS achievement_unlocks CASCADE;

BEGIN;

-- Achievement Unlocks table
CREATE TABLE IF NOT EXISTS achievement_unlocks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  achievement_id UUID NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
  unlocked_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  metadata JSONB,
  notified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Ensure users can't unlock the same achievement twice
CREATE UNIQUE INDEX idx_achievement_unlocks_unique ON achievement_unlocks(user_id, achievement_id);

-- Create indexes for efficient querying
CREATE INDEX idx_achievement_unlocks_user ON achievement_unlocks(user_id);
CREATE INDEX idx_achievement_unlocks_achievement ON achievement_unlocks(achievement_id);
CREATE INDEX idx_achievement_unlocks_date ON achievement_unlocks(unlocked_at DESC);
CREATE INDEX idx_achievement_unlocks_notified ON achievement_unlocks(notified) WHERE notified = FALSE;

-- Enable RLS
ALTER TABLE achievement_unlocks ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own achievement unlocks" ON achievement_unlocks
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage achievement unlocks" ON achievement_unlocks
  FOR ALL USING (true);

-- View for user achievement statistics
CREATE OR REPLACE VIEW user_achievement_stats AS
SELECT 
  u.id as user_id,
  COUNT(au.id) as total_unlocked,
  COUNT(a.id) as total_achievements,
  COALESCE(SUM(a.points), 0) as total_points,
  COALESCE(
    ROUND(
      (COUNT(au.id)::DECIMAL / NULLIF(COUNT(a.id), 0)) * 100, 
      2
    ), 
    0
  ) as completion_percentage,
  MAX(au.unlocked_at) as last_unlock_date
FROM users u
CROSS JOIN achievements a
LEFT JOIN achievement_unlocks au ON u.id = au.user_id AND a.id = au.achievement_id
GROUP BY u.id;

-- Function to unlock achievement
CREATE OR REPLACE FUNCTION unlock_achievement(
  p_user_id UUID,
  p_achievement_code VARCHAR,
  p_project_id UUID DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL
)
RETURNS TABLE (
  success BOOLEAN,
  achievement_id UUID,
  achievement_name VARCHAR,
  points INTEGER,
  already_unlocked BOOLEAN
) AS $$
DECLARE
  v_achievement achievements%ROWTYPE;
  v_existing_unlock UUID;
BEGIN
  -- Find achievement by code
  SELECT * INTO v_achievement
  FROM achievements
  WHERE code = p_achievement_code;

  IF NOT FOUND THEN
    RETURN QUERY SELECT 
      FALSE::BOOLEAN, 
      NULL::UUID, 
      NULL::VARCHAR, 
      0::INTEGER, 
      FALSE::BOOLEAN;
    RETURN;
  END IF;

  -- Check if already unlocked
  SELECT id INTO v_existing_unlock
  FROM achievement_unlocks
  WHERE user_id = p_user_id AND achievement_id = v_achievement.id;

  IF FOUND THEN
    RETURN QUERY SELECT 
      TRUE::BOOLEAN, 
      v_achievement.id, 
      v_achievement.name, 
      v_achievement.points, 
      TRUE::BOOLEAN;
    RETURN;
  END IF;

  -- Unlock the achievement
  INSERT INTO achievement_unlocks (
    user_id, achievement_id, project_id, metadata
  ) VALUES (
    p_user_id, v_achievement.id, p_project_id, p_metadata
  );

  RETURN QUERY SELECT 
    TRUE::BOOLEAN, 
    v_achievement.id, 
    v_achievement.name, 
    v_achievement.points, 
    FALSE::BOOLEAN;
END;
$$ LANGUAGE plpgsql;

-- Function to check and unlock milestones
CREATE OR REPLACE FUNCTION check_milestone_achievements(p_user_id UUID)
RETURNS TABLE (
  achievement_code VARCHAR,
  unlocked BOOLEAN
) AS $$
BEGIN
  -- Word count milestones
  RETURN QUERY
  WITH word_stats AS (
    SELECT 
      u.id as user_id,
      COALESCE(SUM(c.word_count), 0) as total_words
    FROM users u
    LEFT JOIN projects p ON u.id = p.user_id
    LEFT JOIN chapters c ON p.id = c.project_id
    WHERE u.id = p_user_id
    GROUP BY u.id
  )
  SELECT 
    'words_' || milestone::VARCHAR as achievement_code,
    unlock_achievement(
      p_user_id, 
      'words_' || milestone::VARCHAR
    ) as unlocked
  FROM (VALUES (1000), (5000), (10000), (50000), (100000), (500000), (1000000)) as milestones(milestone)
  WHERE milestone <= (SELECT total_words FROM word_stats);

  -- Chapter count milestones
  RETURN QUERY
  WITH chapter_stats AS (
    SELECT 
      COUNT(DISTINCT c.id) as total_chapters
    FROM chapters c
    JOIN projects p ON c.project_id = p.id
    WHERE p.user_id = p_user_id
      AND c.status = 'completed'
  )
  SELECT 
    'chapters_' || milestone::VARCHAR as achievement_code,
    unlock_achievement(
      p_user_id, 
      'chapters_' || milestone::VARCHAR
    ) as unlocked
  FROM (VALUES (1), (5), (10), (25), (50), (100)) as milestones(milestone)
  WHERE milestone <= (SELECT total_chapters FROM chapter_stats);

  -- Project milestones
  RETURN QUERY
  WITH project_stats AS (
    SELECT 
      COUNT(*) as total_projects
    FROM projects
    WHERE user_id = p_user_id
      AND status = 'completed'
  )
  SELECT 
    'projects_' || milestone::VARCHAR as achievement_code,
    unlock_achievement(
      p_user_id, 
      'projects_' || milestone::VARCHAR
    ) as unlocked
  FROM (VALUES (1), (3), (5), (10)) as milestones(milestone)
  WHERE milestone <= (SELECT total_projects FROM project_stats);
END;
$$ LANGUAGE plpgsql;

-- Trigger to check achievements after chapter updates
CREATE OR REPLACE FUNCTION check_achievements_on_chapter_update()
RETURNS TRIGGER AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Get user ID from project
  SELECT user_id INTO v_user_id
  FROM projects
  WHERE id = NEW.project_id;

  -- Check milestone achievements
  PERFORM check_milestone_achievements(v_user_id);

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_check_achievements_on_chapter
  AFTER INSERT OR UPDATE OF word_count, status ON chapters
  FOR EACH ROW
  EXECUTE FUNCTION check_achievements_on_chapter_update();

COMMIT;