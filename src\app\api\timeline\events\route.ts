import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { db } from '@/lib/db/client';
import { getTimelineValidator } from '@/lib/timeline/timeline-instances';
import { requireProjectAccess } from '@/lib/api/auth-middleware';
import { logger } from '@/lib/services/logger'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    // Check authentication and project access
    const authResult = await requireProjectAccess(request, projectId, 'viewer');
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const validator = getTimelineValidator(projectId);
    
    // Get all events from database
    const events = await db.timelineEvents.getAll(projectId);
    
    // Validate timeline
    const validation = await validator.validateTimeline();
    
    return NextResponse.json({
      success: true,
      events,
      validation
    });

  } catch (error) {
    logger.error('Error getting timeline events:', error);
    return NextResponse.json(
      { error: 'Failed to get timeline events' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { projectId, chapterContent, chapterNumber } = await request.json();
    
    if (!projectId || !chapterContent || !chapterNumber) {
      return NextResponse.json(
        { error: 'Project ID, chapter content, and chapter number are required' },
        { status: 400 }
      );
    }

    // Check authentication and project access (editor access needed to add events)
    const authResult = await requireProjectAccess(request, projectId, 'editor');
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const validator = getTimelineValidator(projectId);
    
    // Extract timeline events from chapter content
    const extractedEvents = await validator.extractTimelineFromChapter(
      chapterContent,
      chapterNumber
    );

    // Add events to timeline
    const eventIds = [];
    for (const event of extractedEvents) {
      const eventId = await validator.addEvent(event);
      eventIds.push(eventId);
    }

    // Validate updated timeline
    const validation = await validator.validateTimeline();
    
    return NextResponse.json({
      success: true,
      extractedEvents: eventIds.length,
      validation
    });

  } catch (error) {
    logger.error('Error adding timeline events:', error);
    return NextResponse.json(
      { error: 'Failed to add timeline events' },
      { status: 500 }
    );
  }
}

