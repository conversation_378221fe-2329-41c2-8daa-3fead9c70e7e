import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { VoiceProfileManager } from '@/lib/services/voice-profile-manager';
import { logger } from '@/lib/services/logger'

const voiceProfileManager = new VoiceProfileManager();

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') as 'author' | 'character' | 'narrator' | null;
    const projectId = searchParams.get('projectId');
    const seriesId = searchParams.get('seriesId');
    const isGlobal = searchParams.get('isGlobal') === 'true';

    const supabase = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const profiles = await voiceProfileManager.getUserVoiceProfiles(user.id, {
      type: type || undefined,
      projectId: projectId || undefined,
      seriesId: seriesId || undefined,
      isGlobal: isGlobal || undefined,
    });

    return NextResponse.json({ profiles });
  } catch (error) {
    logger.error('Error fetching voice profiles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch voice profiles' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, type, projectId, seriesId, characterId, isGlobal } = body;

    if (!name || !type) {
      return NextResponse.json(
        { error: 'Name and type are required' },
        { status: 400 }
      );
    }

    const profile = await voiceProfileManager.createVoiceProfile({
      name,
      description,
      type,
      projectId,
      seriesId,
      characterId,
      isGlobal,
    });

    if (!profile) {
      return NextResponse.json(
        { error: 'Failed to create voice profile' },
        { status: 500 }
      );
    }

    return NextResponse.json({ profile });
  } catch (error) {
    logger.error('Error creating voice profile:', error);
    return NextResponse.json(
      { error: 'Failed to create voice profile' },
      { status: 500 }
    );
  }
}