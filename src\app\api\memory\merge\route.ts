import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { getMemoryManager } from '@/lib/memory/memory-instances';
import { authenticateUser } from '@/lib/auth';
import { db } from '@/lib/db/client';
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }
    const { user } = authResult;
    
    const { projectId } = await request.json();
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    // Verify user owns the project
    const project = await db.projects.getById(projectId);
    if (!project || project.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 403 }
      );
    }

    const memoryManager = getMemoryManager(projectId);
    
    // Merge similar memories
    const mergeResult = await memoryManager.mergeSimilarMemories();
    const stats = memoryManager.getMemoryStats();
    
    return NextResponse.json({
      success: true,
      message: `Successfully merged ${mergeResult.mergedCount} similar memory chunks`,
      mergeResult,
      stats
    });

  } catch (error) {
    logger.error('Error merging memory:', error);
    return NextResponse.json(
      { error: 'Failed to merge memory' },
      { status: 500 }
    );
  }
}