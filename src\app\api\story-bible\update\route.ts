import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import type { SupabaseClient } from '@supabase/supabase-js'
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { projectId, type, id, data, action } = body

    if (!projectId || !type || !action) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Verify project ownership
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    switch (action) {
      case 'create':
        await handleCreate(supabase, projectId, type, data)
        break
      case 'update':
        await handleUpdate(supabase, projectId, type, id, data)
        break
      case 'delete':
        await handleDelete(supabase, projectId, type, id)
        break
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    logger.error('Story bible update error:', error)
    return NextResponse.json(
      { error: 'Failed to update story bible' },
      { status: 500 }
    )
  }
}

async function handleCreate(supabase: SupabaseClient, projectId: string, type: string, data: Record<string, unknown>) {
  switch (type) {
    case 'character':
      await supabase.from('characters').insert({
        project_id: projectId,
        name: data.name,
        role: data.role,
        description: data.description,
        backstory: data.backstory,
        personality_traits: {
          traits: data.traits || [],
          motivations: [],
          fears: [],
          goals: []
        },
        relationships: []
      })
      break

    case 'world_rule':
      await supabase.from('story_bible').insert({
        project_id: projectId,
        entry_type: 'world_rule',
        entry_key: data.key,
        entry_data: { value: data.value }
      })
      break

    case 'timeline_event':
      await supabase.from('story_bible').insert({
        project_id: projectId,
        entry_type: 'timeline_event',
        entry_key: `event_${Date.now()}`,
        entry_data: { event: data.event, chapter: data.chapter },
        chapter_introduced: data.chapter
      })
      break

    case 'plot_thread':
      await supabase.from('story_bible').insert({
        project_id: projectId,
        entry_type: 'plot_thread',
        entry_key: `thread_${Date.now()}`,
        entry_data: { description: data.description, status: data.status }
      })
      break
  }
}

async function handleUpdate(supabase: SupabaseClient, projectId: string, type: string, id: string, data: Record<string, unknown>) {
  switch (type) {
    case 'character':
      await supabase
        .from('characters')
        .update({
          name: data.name,
          role: data.role,
          description: data.description,
          backstory: data.backstory,
          personality_traits: {
            traits: data.traits || [],
            motivations: [],
            fears: [],
            goals: []
          }
        })
        .eq('id', id)
        .eq('project_id', projectId)
      break

    case 'world_rule':
      await supabase
        .from('story_bible')
        .update({
          entry_data: { value: data.value }
        })
        .eq('entry_key', data.key)
        .eq('entry_type', 'world_rule')
        .eq('project_id', projectId)
      break

    case 'timeline_event':
      await supabase
        .from('story_bible')
        .update({
          entry_data: { event: data.event, chapter: data.chapter },
          chapter_introduced: data.chapter
        })
        .eq('id', id)
        .eq('entry_type', 'timeline_event')
        .eq('project_id', projectId)
      break

    case 'plot_thread':
      await supabase
        .from('story_bible')
        .update({
          entry_data: { description: data.description, status: data.status }
        })
        .eq('entry_key', id)
        .eq('entry_type', 'plot_thread')
        .eq('project_id', projectId)
      break
  }
}

async function handleDelete(supabase: SupabaseClient, projectId: string, type: string, id: string) {
  switch (type) {
    case 'character':
      await supabase
        .from('characters')
        .delete()
        .eq('id', id)
        .eq('project_id', projectId)
      break

    case 'world_rule':
      await supabase
        .from('story_bible')
        .delete()
        .eq('entry_key', id)
        .eq('entry_type', 'world_rule')
        .eq('project_id', projectId)
      break

    case 'timeline_event':
      await supabase
        .from('story_bible')
        .delete()
        .eq('id', id)
        .eq('entry_type', 'timeline_event')
        .eq('project_id', projectId)
      break

    case 'plot_thread':
      await supabase
        .from('story_bible')
        .delete()
        .eq('entry_key', id)
        .eq('entry_type', 'plot_thread')
        .eq('project_id', projectId)
      break
  }
}