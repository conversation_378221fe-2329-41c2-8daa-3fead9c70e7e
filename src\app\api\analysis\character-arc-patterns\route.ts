import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import type { ArcPattern, Deviation } from '@/lib/types/character-development';
import { ARC_PATTERNS } from '@/lib/types/character-development';
import { logger } from '@/lib/services/logger'

interface Character {
  id: string;
  name: string;
  role: string;
  character_arc?: {
    type?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

interface Chapter {
  id: string;
  chapter_number: number;
  content?: string;
  outline?: string;
  ai_notes?: unknown;
}

interface ContentAnalysis {
  characterName: string;
  role: string;
  arcType?: string;
  totalChapters: number;
  characterPresence: number[];
  emotionalProgression: number[];
  conflictIntensity: number[];
  growthMoments: { chapter: number; type: string; intensity: number }[];
  relationshipEvents: { chapter: number; type: string; impact: number }[];
}

interface PatternMatch {
  id: string;
  name: string;
  phases: string[];
  confidence: number;
  segments: { start: number; end: number }[];
}

interface ArcPrediction {
  currentPhase: string;
  completion: { minChapter: number; maxChapter: number };
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { characterId, projectId } = await request.json();

    if (!characterId || !projectId) {
      return NextResponse.json(
        { error: 'Character ID and Project ID are required' },
        { status: 400 }
      );
    }

    // Verify user has access to this project
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', projectId)
      .single();

    if (!project || project.user_id !== user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Fetch character data
    const { data: character, error: charError } = await supabase
      .from('characters')
      .select('*')
      .eq('id', characterId)
      .eq('project_id', projectId)
      .single();

    if (charError || !character) {
      return NextResponse.json(
        { error: 'Character not found' },
        { status: 404 }
      );
    }

    // Fetch chapters for this project
    const { data: chapters, error: chaptersError } = await supabase
      .from('chapters')
      .select('id, chapter_number, content, outline, ai_notes')
      .eq('project_id', projectId)
      .order('chapter_number', { ascending: true });

    if (chaptersError) {
      throw new Error('Failed to fetch chapters');
    }

    // Analyze arc pattern
    const arcPattern = await analyzeArcPattern(character, chapters || []);

    return NextResponse.json(arcPattern);
  } catch (error) {
    logger.error('Error analyzing arc pattern:', error);
    return NextResponse.json(
      { error: 'Failed to analyze arc pattern' },
      { status: 500 }
    );
  }
}

async function analyzeArcPattern(character: Character, chapters: Chapter[]): Promise<ArcPattern> {
  // Extract character arc type from character data
  const characterArcType = character.character_arc?.type || 'unknown';
  
  // Analyze content for pattern matching
  const contentAnalysis = analyzeCharacterContent(character, chapters);
  
  // Determine best matching pattern
  const patternMatch = findBestPatternMatch(characterArcType, contentAnalysis, chapters);
  
  // Find deviations from expected pattern
  const deviations = findPatternDeviations(patternMatch, contentAnalysis, chapters);
  
  // Predict current phase and completion
  const predictions = predictArcProgression(patternMatch, contentAnalysis, chapters);

  return {
    type: patternMatch.id,
    name: patternMatch.name,
    confidence: patternMatch.confidence,
    matchedSegments: patternMatch.segments,
    deviations,
    currentPhase: predictions.currentPhase,
    predictedCompletion: predictions.completion
  };
}

function analyzeCharacterContent(character: Character, chapters: Chapter[]): ContentAnalysis {
  const analysis = {
    characterName: character.name,
    role: character.role,
    arcType: character.character_arc?.type,
    totalChapters: chapters.length,
    characterPresence: [] as number[],
    emotionalProgression: [] as number[],
    conflictIntensity: [] as number[],
    growthMoments: [] as { chapter: number; type: string; intensity: number }[],
    relationshipEvents: [] as { chapter: number; type: string; impact: number }[]
  };

  chapters.forEach((chapter) => {
    const content = (chapter.content || '') + (chapter.outline || '');
    const characterMentions = countOccurrences(content, character.name);
    
    // Character presence score
    analysis.characterPresence.push(Math.min(characterMentions / 10, 1) * 100);
    
    // Emotional progression analysis
    const emotionalScore = analyzeEmotionalContent(content, character.name);
    analysis.emotionalProgression.push(emotionalScore);
    
    // Conflict intensity
    const conflictScore = analyzeConflictContent(content, character.name);
    analysis.conflictIntensity.push(conflictScore);
    
    // Growth moments
    const growthEvents = identifyGrowthMoments(content, chapter.chapter_number);
    analysis.growthMoments.push(...growthEvents);
    
    // Relationship events
    const relationshipEvents = identifyRelationshipEvents(content, character.name, chapter.chapter_number);
    analysis.relationshipEvents.push(...relationshipEvents);
  });

  return analysis;
}

function findBestPatternMatch(characterArcType: string, analysis: ContentAnalysis, chapters: Chapter[]): PatternMatch {
  const patterns = ARC_PATTERNS.map(pattern => {
    const confidence = calculatePatternConfidence(pattern, analysis, chapters);
    return {
      ...pattern,
      confidence,
      segments: identifyPatternSegments(pattern, analysis, chapters)
    };
  });

  // Sort by confidence and return best match
  patterns.sort((a, b) => b.confidence - a.confidence);
  const bestMatch = patterns[0];

  // If character has explicit arc type, boost confidence for matching patterns
  if (characterArcType !== 'unknown') {
    const explicitMatch = patterns.find(p => p.id.includes(characterArcType.replace('_', '-')));
    if (explicitMatch && explicitMatch.confidence > 60) {
      return explicitMatch;
    }
  }

  return bestMatch || patterns[0]!;
}

function calculatePatternConfidence(pattern: typeof ARC_PATTERNS[0], analysis: ContentAnalysis, chapters: Chapter[]): number {
  let confidence = 0;

  // Check if emotional progression matches pattern expectations
  if (pattern.id === 'heros-journey') {
    // Hero's journey should show initial stability, then challenges, then growth
    const early = analysis.emotionalProgression.slice(0, Math.floor(chapters.length * 0.3));
    const middle = analysis.emotionalProgression.slice(
      Math.floor(chapters.length * 0.3), 
      Math.floor(chapters.length * 0.7)
    );
    const late = analysis.emotionalProgression.slice(Math.floor(chapters.length * 0.7));

    const earlyAvg = early.reduce((a, b) => a + b, 0) / early.length || 0;
    const middleAvg = middle.reduce((a, b) => a + b, 0) / middle.length || 0;
    const lateAvg = late.reduce((a, b) => a + b, 0) / late.length || 0;

    if (middleAvg < earlyAvg && lateAvg > middleAvg) confidence += 30;
    if (analysis.growthMoments.length >= 3) confidence += 20;
  }

  if (pattern.id === 'tragic-fall') {
    // Tragic fall should show decline over time
    const firstHalf = analysis.emotionalProgression.slice(0, Math.floor(chapters.length / 2));
    const secondHalf = analysis.emotionalProgression.slice(Math.floor(chapters.length / 2));
    
    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length || 0;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length || 0;

    if (firstAvg > secondAvg) confidence += 25;
    if (analysis.conflictIntensity.some(c => c > 70)) confidence += 15;
  }

  if (pattern.id === 'redemption-arc') {
    // Redemption should show low start, rock bottom, then recovery
    const quarters = [
      analysis.emotionalProgression.slice(0, Math.floor(chapters.length * 0.25)),
      analysis.emotionalProgression.slice(Math.floor(chapters.length * 0.25), Math.floor(chapters.length * 0.5)),
      analysis.emotionalProgression.slice(Math.floor(chapters.length * 0.5), Math.floor(chapters.length * 0.75)),
      analysis.emotionalProgression.slice(Math.floor(chapters.length * 0.75))
    ];

    const quarterAvgs = quarters.map(q => q.reduce((a, b) => a + b, 0) / (q.length || 1));
    
    const q0 = quarterAvgs[0];
    const q1 = quarterAvgs[1];
    const q2 = quarterAvgs[2];
    const q3 = quarterAvgs[3];
    
    if (q0 !== undefined && q1 !== undefined && q2 !== undefined && q3 !== undefined && 
        q1 < q0 && q3 > q2) confidence += 30;
    if (analysis.growthMoments.some(g => g.type === 'breakthrough')) confidence += 20;
  }

  // Base confidence from character presence and activity
  const avgPresence = analysis.characterPresence.reduce((a: number, b: number) => a + b, 0) / analysis.characterPresence.length;
  confidence += Math.min(avgPresence / 2, 25);

  // Bonus for consistent character development
  if (analysis.growthMoments.length > 0) confidence += 10;
  if (analysis.relationshipEvents.length > 0) confidence += 10;

  return Math.min(Math.max(confidence, 0), 100);
}

function identifyPatternSegments(pattern: typeof ARC_PATTERNS[0], analysis: ContentAnalysis, chapters: Chapter[]): { start: number; end: number }[] {
  const segments = [];
  const phaseCount = pattern.phases.length;
  const chaptersPerPhase = Math.ceil(chapters.length / phaseCount);

  for (let i = 0; i < phaseCount; i++) {
    const start = i * chaptersPerPhase + 1;
    const end = Math.min((i + 1) * chaptersPerPhase, chapters.length);
    
    // Check if this segment has appropriate character activity
    const segmentPresence = analysis.characterPresence.slice(start - 1, end);
    const avgPresence = segmentPresence.reduce((a: number, b: number) => a + b, 0) / segmentPresence.length;
    
    if (avgPresence > 20) { // Threshold for meaningful presence
      segments.push({ start, end });
    }
  }

  return segments;
}

function findPatternDeviations(pattern: PatternMatch, analysis: ContentAnalysis, chapters: Chapter[]): Deviation[] {
  const deviations: Deviation[] = [];

  // Check for character absence in expected active phases
  pattern.segments.forEach((segment: { start: number; end: number }, index: number) => {
    const segmentPresence = analysis.characterPresence.slice(segment.start - 1, segment.end);
    const avgPresence = segmentPresence.reduce((a: number, b: number) => a + b, 0) / segmentPresence.length;
    
    if (avgPresence < 30) {
      deviations.push({
        chapter: segment.start,
        dimension: 'Character Presence',
        severity: avgPresence < 10 ? 'major' : 'moderate',
        description: `Character has low presence during ${pattern.phases[index]} phase`,
        suggestion: `Increase character involvement in chapters ${segment.start}-${segment.end} to strengthen arc development`
      });
    }
  });

  // Check for emotional inconsistencies
  const emotionalVariance = calculateVariance(analysis.emotionalProgression);
  if (emotionalVariance > 1000) {
    deviations.push({
      chapter: Math.floor(chapters.length / 2),
      dimension: 'Emotional Consistency',
      severity: 'moderate',
      description: 'Character shows erratic emotional progression',
      suggestion: 'Smooth out emotional transitions to create more believable character development'
    });
  }

  // Check for missing growth moments in key phases
  if (pattern.id === 'heros-journey' && analysis.growthMoments.length < 2) {
    deviations.push({
      chapter: Math.floor(chapters.length * 0.7),
      dimension: 'Character Growth',
      severity: 'major',
      description: 'Insufficient growth moments for Hero\'s Journey pattern',
      suggestion: 'Add more character development scenes showing internal change and revelation'
    });
  }

  return deviations;
}

function predictArcProgression(pattern: PatternMatch, analysis: ContentAnalysis, chapters: Chapter[]): ArcPrediction {
  const phaseCount = pattern.phases.length;
  const chaptersPerPhase = Math.ceil(chapters.length / phaseCount);
  
  // Determine current phase based on latest chapter activity
  let currentPhaseIndex = Math.min(
    Math.floor((chapters.length - 1) / chaptersPerPhase),
    phaseCount - 1
  );

  // Adjust based on growth moments
  const recentGrowth = analysis.growthMoments.filter(
    (g) => g.chapter > chapters.length - chaptersPerPhase
  );
  
  if (recentGrowth.length > 0 && currentPhaseIndex < phaseCount - 1) {
    currentPhaseIndex = Math.min(currentPhaseIndex + 1, phaseCount - 1);
  }

  const currentPhase = pattern.phases[currentPhaseIndex] || '';

  // Predict completion based on pattern progression
  const remainingPhases = phaseCount - currentPhaseIndex - 1;
  const estimatedChaptersNeeded = remainingPhases * chaptersPerPhase;
  
  const completion = {
    minChapter: chapters.length + Math.floor(estimatedChaptersNeeded * 0.8),
    maxChapter: chapters.length + Math.ceil(estimatedChaptersNeeded * 1.2)
  };

  return { currentPhase, completion };
}

// Helper functions
function countOccurrences(text: string, searchTerm: string): number {
  const regex = new RegExp(searchTerm, 'gi');
  const matches = text.match(regex);
  return matches ? matches.length : 0;
}

function analyzeEmotionalContent(content: string, characterName: string): number {
  const positiveWords = ['happy', 'joy', 'smile', 'laugh', 'love', 'hope', 'confident', 'proud', 'peaceful'];
  const negativeWords = ['sad', 'angry', 'fear', 'worry', 'despair', 'hate', 'frustrated', 'defeated', 'lost'];
  
  let score = 50; // Neutral baseline
  
  if (content.includes(characterName)) {
    positiveWords.forEach(word => {
      if (content.toLowerCase().includes(word)) score += 5;
    });
    
    negativeWords.forEach(word => {
      if (content.toLowerCase().includes(word)) score -= 5;
    });
  }
  
  return Math.max(0, Math.min(100, score));
}

function analyzeConflictContent(content: string, characterName: string): number {
  const conflictWords = ['fight', 'struggle', 'battle', 'conflict', 'argue', 'confront', 'challenge', 'oppose'];
  let score = 0;
  
  if (content.includes(characterName)) {
    conflictWords.forEach(word => {
      if (content.toLowerCase().includes(word)) score += 10;
    });
  }
  
  return Math.min(100, score);
}

function identifyGrowthMoments(content: string, chapterNumber: number) {
  const growthKeywords = {
    'breakthrough': ['realized', 'understood', 'epiphany', 'revelation', 'breakthrough'],
    'decision': ['decided', 'chose', 'determined', 'resolved'],
    'growth': ['learned', 'grew', 'developed', 'matured', 'evolved']
  };
  
  const moments: Array<{ chapter: number; type: string; intensity: number }> = [];
  
  Object.entries(growthKeywords).forEach(([type, keywords]) => {
    keywords.forEach(keyword => {
      if (content.toLowerCase().includes(keyword)) {
        moments.push({
          chapter: chapterNumber,
          type,
          intensity: type === 'breakthrough' ? 80 : type === 'decision' ? 60 : 40
        });
      }
    });
  });
  
  return moments;
}

function identifyRelationshipEvents(content: string, characterName: string, chapterNumber: number) {
  const relationshipKeywords = {
    'positive': ['befriended', 'allied', 'helped', 'saved', 'supported'],
    'negative': ['betrayed', 'abandoned', 'hurt', 'fought', 'argued'],
    'romantic': ['kissed', 'loved', 'married', 'romance', 'attracted']
  };
  
  const events: Array<{ chapter: number; type: string; impact: number }> = [];
  
  if (content.includes(characterName)) {
    Object.entries(relationshipKeywords).forEach(([type, keywords]) => {
      keywords.forEach(keyword => {
        if (content.toLowerCase().includes(keyword)) {
          events.push({
            chapter: chapterNumber,
            type,
            impact: type === 'romantic' ? 70 : type === 'positive' ? 50 : -40
          });
        }
      });
    });
  }
  
  return events;
}

function calculateVariance(numbers: number[]): number {
  const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
  const squaredDiffs = numbers.map(n => Math.pow(n - mean, 2));
  return squaredDiffs.reduce((a, b) => a + b, 0) / numbers.length;
}