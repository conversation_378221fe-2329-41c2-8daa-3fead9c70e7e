'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Users, RefreshCw, WifiOff, UserX, ArrowLeft } from 'lucide-react'
import { logger } from '@/lib/services/logger'

interface Props {
  children: React.ReactNode
  onReconnect?: () => void
  sessionId?: string
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  isConnectionError: boolean
  isPermissionError: boolean
}

export class CollaborationErrorBoundary extends React.Component<Props, State> {
  private reconnectAttempts = 0
  private maxReconnectAttempts = 3

  constructor(props: Props) {
    super(props)
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      isConnectionError: false,
      isPermissionError: false
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorMessage = error.message.toLowerCase()
    
    return { 
      hasError: true, 
      error,
      isConnectionError: errorMessage.includes('connection') || errorMessage.includes('websocket') || errorMessage.includes('realtime'),
      isPermissionError: errorMessage.includes('permission') || errorMessage.includes('access denied') || errorMessage.includes('unauthorized')
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    logger.error('Collaboration error boundary caught error', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      sessionId: this.props.sessionId
    })

    this.setState({
      errorInfo
    })

    // Auto-retry connection errors
    if (this.state.isConnectionError && this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.handleReconnect()
      }, 2000 * Math.pow(2, this.reconnectAttempts)) // Exponential backoff
    }
  }

  handleReset = () => {
    this.reconnectAttempts = 0
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      isConnectionError: false,
      isPermissionError: false
    })
  }

  handleReconnect = () => {
    this.reconnectAttempts++
    this.handleReset()
    this.props.onReconnect?.()
  }

  handleWorkOffline = () => {
    // Disable collaboration and continue in offline mode
    localStorage.setItem('collaboration-offline-mode', 'true')
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      const { isConnectionError, isPermissionError } = this.state

      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <Card className="max-w-lg w-full">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-orange-500/10 flex items-center justify-center">
                {isConnectionError ? (
                  <WifiOff className="h-6 w-6 text-orange-500" />
                ) : isPermissionError ? (
                  <UserX className="h-6 w-6 text-orange-500" />
                ) : (
                  <Users className="h-6 w-6 text-orange-500" />
                )}
              </div>
              <CardTitle>
                {isConnectionError 
                  ? "Connection Lost"
                  : isPermissionError
                  ? "Access Denied"
                  : "Collaboration Error"}
              </CardTitle>
              <CardDescription>
                {isConnectionError 
                  ? "We're having trouble connecting to the collaboration server."
                  : isPermissionError
                  ? "You don't have permission to access this collaborative session."
                  : "Something went wrong with real-time collaboration."}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isConnectionError && (
                <Alert>
                  <AlertDescription>
                    {this.reconnectAttempts > 0 
                      ? `Reconnection attempt ${this.reconnectAttempts} of ${this.maxReconnectAttempts}...`
                      : "Check your internet connection and try again."}
                  </AlertDescription>
                </Alert>
              )}

              {isPermissionError && (
                <Alert>
                  <AlertDescription>
                    Make sure you have been invited to this project and have accepted the invitation.
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                {isConnectionError && (
                  <>
                    <Button 
                      onClick={this.handleReconnect}
                      className="w-full"
                      variant="default"
                      disabled={this.reconnectAttempts >= this.maxReconnectAttempts}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Try Reconnecting
                    </Button>

                    <Button 
                      onClick={this.handleWorkOffline}
                      className="w-full"
                      variant="outline"
                    >
                      <WifiOff className="h-4 w-4 mr-2" />
                      Continue Offline
                    </Button>
                  </>
                )}

                {isPermissionError && (
                  <Button 
                    onClick={() => window.location.href = '/dashboard'}
                    className="w-full"
                    variant="default"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Dashboard
                  </Button>
                )}

                {!isConnectionError && !isPermissionError && (
                  <Button 
                    onClick={this.handleReset}
                    className="w-full"
                    variant="default"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                )}
              </div>

              <p className="text-sm text-muted-foreground text-center">
                Your work is automatically saved locally. No changes will be lost.
              </p>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-4">
                  <summary className="text-sm text-muted-foreground cursor-pointer">
                    Error Details
                  </summary>
                  <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                    {this.state.error.stack}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}