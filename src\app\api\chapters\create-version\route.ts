import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { VersionHistoryService } from '@/lib/version-history'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'

// Validation schema for version creation
const createVersionSchema = z.object({
  chapterId: z.string().uuid('Invalid chapter ID'),
  changeSummary: z.string().min(1, 'Change summary is required').max(500, 'Change summary must be 500 characters or less')
});

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate request body
    const validationResult = createVersionSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: validationResult.error.errors 
      }, { status: 400 })
    }
    
    const { chapterId, changeSummary } = validationResult.data

    // Verify user owns the chapter
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select('project_id, user_id')
      .eq('id', chapterId)
      .single()

    if (chapterError || !chapter) {
      return NextResponse.json({ error: 'Chapter not found' }, { status: 404 })
    }

    // Verify user owns the project (chapters don't have user_id directly)
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', chapter.project_id)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    // Create manual version
    const versionService = new VersionHistoryService()
    const success = await versionService.createManualVersion(chapterId, user.id, changeSummary)

    if (!success) {
      return NextResponse.json({ error: 'Failed to create version' }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: error.errors 
      }, { status: 400 })
    }
    logger.error('Create version error:', error)
    return NextResponse.json(
      { error: 'Failed to create version' },
      { status: 500 }
    )
  }
}