import { logger } from '@/lib/services/logger'
/**
 * Theme Registry for BookScribe AI
 * Manages all available themes and provides utilities for theme switching
 */

import { 
  ThemeDefinition, 
  writersSanctuaryLight, 
  eveningStudyDark, 
  forestManuscriptLight, 
  midnightInkDark 
} from './theme-definitions';

// Re-export types for external consumption
export type { ThemeDefinition } from './theme-definitions';

// Registry of all available themes
export const themeRegistry: Record<string, ThemeDefinition> = {
  [writersSanctuaryLight.id]: writersSanctuaryLight,
  [eveningStudyDark.id]: eveningStudyDark,
  [forestManuscriptLight.id]: forestManuscriptLight,
  [midnightInkDark.id]: midnightInkDark,
};

// Default themes
export const DEFAULT_LIGHT_THEME = writersSanctuaryLight.id;
export const DEFAULT_DARK_THEME = eveningStudyDark.id;

// Theme categories for organization
export const themeCategories = {
  light: [
    writersSanctuaryLight.id,
    forestManuscriptLight.id,
  ],
  dark: [
    eveningStudyDark.id,
    midnightInkDark.id,
  ],
};

/**
 * Get all available themes
 */
export function getAllThemes(): ThemeDefinition[] {
  return Object.values(themeRegistry);
}

/**
 * Get themes by mode (light/dark)
 */
export function getThemesByMode(mode: 'light' | 'dark'): ThemeDefinition[] {
  return getAllThemes().filter(theme => theme.mode === mode);
}

/**
 * Get a theme by ID
 */
export function getThemeById(id: string): ThemeDefinition | null {
  return themeRegistry[id] || null;
}

/**
 * Get the default theme for a mode
 */
export function getDefaultTheme(mode: 'light' | 'dark'): ThemeDefinition {
  const defaultId = mode === 'light' ? DEFAULT_LIGHT_THEME : DEFAULT_DARK_THEME;
  const theme = themeRegistry[defaultId];
  if (!theme) {
    throw new Error(`Default ${mode} theme "${defaultId}" not found in registry`);
  }
  return theme;
}

/**
 * Check if a theme exists
 */
export function themeExists(id: string): boolean {
  return id in themeRegistry;
}

/**
 * Get theme display name with fallback
 */
export function getThemeDisplayName(id: string): string {
  const theme = getThemeById(id);
  return theme?.name || 'Unknown Theme';
}

/**
 * Convert theme colors to CSS custom properties
 */
export function themeToCSSProperties(theme: ThemeDefinition): Record<string, string> {
  const { colors } = theme;
  
  return {
    // Base colors
    '--theme-base': colors.base,
    '--theme-default': colors.default,
    '--theme-faded': colors.faded,
    '--theme-red': colors.red,
    '--theme-orange': colors.orange,
    '--theme-yellow': colors.yellow,
    '--theme-green': colors.green,
    '--theme-cyan': colors.cyan,
    '--theme-blue': colors.blue,
    '--theme-purple': colors.purple,
    
    // Project colors
    '--theme-root': colors.root,
    '--theme-folder': colors.folder,
    '--theme-file': colors.file,
    '--theme-title': colors.title,
    '--theme-chapter': colors.chapter,
    '--theme-scene': colors.scene,
    '--theme-note': colors.note,
    '--theme-active': colors.active,
    '--theme-inactive': colors.inactive,
    '--theme-disabled': colors.disabled,
    
    // Palette colors
    '--theme-window': colors.window,
    '--theme-window-text': colors.windowText,
    '--theme-alternate-base': colors.alternateBase,
    '--theme-text': colors.text,
    '--theme-tooltip-base': colors.tooltipBase,
    '--theme-tooltip-text': colors.tooltipText,
    '--theme-button': colors.button,
    '--theme-button-text': colors.buttonText,
    '--theme-bright-text': colors.brightText,
    '--theme-highlight': colors.highlight,
    '--theme-highlighted-text': colors.highlightedText,
    '--theme-link': colors.link,
    '--theme-link-visited': colors.linkVisited,
    '--theme-accent': colors.accent,
    
    // GUI colors
    '--theme-help-text': colors.helpText,
    '--theme-faded-text': colors.fadedText,
    '--theme-error-text': colors.errorText,
    
    // Syntax colors
    '--theme-background': colors.background,
    '--theme-syntax-text': colors.syntaxText,
    '--theme-line': colors.line,
    '--theme-header-text': colors.headerText,
    '--theme-header-tag': colors.headerTag,
    '--theme-emphasis': colors.emphasis,
    '--theme-dialog': colors.dialog,
    '--theme-alt-dialog': colors.altDialog,
    '--theme-syntax-note': colors.syntaxNote,
    '--theme-hidden': colors.hidden,
    '--theme-shortcode': colors.shortcode,
    '--theme-keyword': colors.keyword,
    '--theme-tag': colors.tag,
    '--theme-value': colors.value,
    '--theme-optional': colors.optional,
    '--theme-spellcheck-line': colors.spellcheckLine,
    '--theme-error-line': colors.errorLine,
    '--theme-replace-tag': colors.replaceTag,
    '--theme-modifier': colors.modifier,
    '--theme-text-highlight': colors.textHighlight,
  };
}

/**
 * Apply theme to document root
 * @deprecated - Theme application is now handled by next-themes
 */
export function applyThemeToDocument(theme: ThemeDefinition): void {
  // This function is deprecated and should not be used
  // Theme application is now handled by next-themes through CSS classes
  logger.warn('applyThemeToDocument is deprecated. Themes are now applied via next-themes.');
}

/**
 * Get theme color with fallback
 */
export function getThemeColor(theme: ThemeDefinition, colorKey: keyof ThemeDefinition['colors'], fallback: string = '#000000'): string {
  return theme.colors[colorKey] || fallback;
}

/**
 * Create a lighter variant of a color
 */
export function lightenColor(color: string, amount: number = 20): string {
  // Simple hex color lightening
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  const r = Math.min(255, (num >> 16) + amount);
  const g = Math.min(255, ((num >> 8) & 0x00FF) + amount);
  const b = Math.min(255, (num & 0x0000FF) + amount);
  return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
}

/**
 * Create a darker variant of a color
 */
export function darkenColor(color: string, amount: number = 20): string {
  // Simple hex color darkening
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  const r = Math.max(0, (num >> 16) - amount);
  const g = Math.max(0, ((num >> 8) & 0x00FF) - amount);
  const b = Math.max(0, (num & 0x0000FF) - amount);
  return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
}

/**
 * Get contrast color (black or white) for a given background
 */
export function getContrastColor(backgroundColor: string): string {
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  return luminance > 0.5 ? '#000000' : '#ffffff';
}
