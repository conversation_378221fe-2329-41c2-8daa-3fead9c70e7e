import { logger } from '@/lib/services/logger'

/**
 * Model access configuration by subscription tier
 */
const MODEL_ACCESS_BY_TIER = {
  free: {
    allowed: ['gpt-4o-mini'],
    maxTokens: 4000,
    features: ['basic_generation']
  },
  starter: {
    allowed: ['gpt-4o-mini', 'gpt-4.1-mini'],
    maxTokens: 16000,
    features: ['basic_generation', 'chapter_planning']
  },
  professional: {
    allowed: ['gpt-4o-mini', 'gpt-4.1-mini', 'gpt-4.1'],
    maxTokens: 128000,
    features: ['basic_generation', 'chapter_planning', 'advanced_editing', 'batch_generation']
  },
  studio: {
    allowed: ['gpt-4o-mini', 'gpt-4.1-mini', 'gpt-4.1', 'text-embedding-3-small', 'text-embedding-3-large'],
    maxTokens: 1000000, // Full 1M context for GPT-4.1
    features: ['basic_generation', 'chapter_planning', 'advanced_editing', 'batch_generation', 'complete_story', 'embeddings']
  }
}

/**
 * Action to feature mapping
 */
const ACTION_FEATURES = {
  generate_structure: 'basic_generation',
  generate_chapter: 'basic_generation',
  write_chapter: 'basic_generation',
  expand_chapter: 'advanced_editing',
  edit_chapter: 'advanced_editing',
  generate_chapter_enhanced: 'chapter_planning',
  generate_complete_story: 'complete_story',
  batch_write_chapters: 'batch_generation',
  regenerate_part: 'advanced_editing'
}

export interface ModelAccessResult {
  allowed: boolean
  reason?: string
  suggestedModel?: string
  requiredTier?: string
}

/**
 * Validate if a user can access a specific AI model
 */
export function validateModelAccess(
  userTier: string,
  requestedModel: string,
  action?: string
): ModelAccessResult {
  const tierConfig = MODEL_ACCESS_BY_TIER[userTier as keyof typeof MODEL_ACCESS_BY_TIER] || MODEL_ACCESS_BY_TIER.free
  
  // Check if model is allowed for tier
  if (!tierConfig.allowed.includes(requestedModel)) {
    // Find the minimum tier that allows this model
    const requiredTier = Object.entries(MODEL_ACCESS_BY_TIER).find(([_, config]) => 
      config.allowed.includes(requestedModel)
    )?.[0]
    
    return {
      allowed: false,
      reason: `Model ${requestedModel} is not available in ${userTier} tier`,
      suggestedModel: tierConfig.allowed[tierConfig.allowed.length - 1], // Best model for their tier
      requiredTier: requiredTier || 'studio'
    }
  }
  
  // Check if action is allowed for tier
  if (action && ACTION_FEATURES[action as keyof typeof ACTION_FEATURES]) {
    const requiredFeature = ACTION_FEATURES[action as keyof typeof ACTION_FEATURES]
    if (!tierConfig.features.includes(requiredFeature)) {
      // Find minimum tier for this feature
      const requiredTier = Object.entries(MODEL_ACCESS_BY_TIER).find(([_, config]) => 
        config.features.includes(requiredFeature)
      )?.[0]
      
      return {
        allowed: false,
        reason: `Action '${action}' requires ${requiredTier} tier or higher`,
        requiredTier: requiredTier || 'studio'
      }
    }
  }
  
  return { allowed: true }
}

/**
 * Get the best available model for a user's tier and use case
 */
export function getBestModelForTier(userTier: string, useCase: 'generation' | 'editing' | 'embedding' = 'generation'): string {
  const tierConfig = MODEL_ACCESS_BY_TIER[userTier as keyof typeof MODEL_ACCESS_BY_TIER] || MODEL_ACCESS_BY_TIER.free
  
  switch (useCase) {
    case 'embedding':
      return tierConfig.allowed.find(m => m.includes('embedding')) || 'text-embedding-3-small'
    case 'editing':
      // Prefer mini models for editing (faster, cheaper)
      return tierConfig.allowed.find(m => m.includes('mini')) || tierConfig.allowed[0]
    case 'generation':
    default:
      // Use the best model available
      return tierConfig.allowed[tierConfig.allowed.length - 1]
  }
}

/**
 * Get token limit for user's tier
 */
export function getTokenLimitForTier(userTier: string): number {
  const tierConfig = MODEL_ACCESS_BY_TIER[userTier as keyof typeof MODEL_ACCESS_BY_TIER] || MODEL_ACCESS_BY_TIER.free
  return tierConfig.maxTokens
}

/**
 * Validate if user can perform a specific action
 */
export function canPerformAction(userTier: string, action: string): boolean {
  const tierConfig = MODEL_ACCESS_BY_TIER[userTier as keyof typeof MODEL_ACCESS_BY_TIER] || MODEL_ACCESS_BY_TIER.free
  const requiredFeature = ACTION_FEATURES[action as keyof typeof ACTION_FEATURES]
  
  if (!requiredFeature) {
    logger.warn(`Unknown action: ${action}`)
    return true // Allow unknown actions by default
  }
  
  return tierConfig.features.includes(requiredFeature)
}

/**
 * Get upgrade message for restricted features
 */
export function getUpgradeMessage(currentTier: string, requiredTier: string, feature: string): string {
  const messages = {
    'complete_story': `Generate complete stories with our advanced AI orchestration - upgrade to ${requiredTier} plan`,
    'batch_generation': `Generate multiple chapters simultaneously - upgrade to ${requiredTier} plan`,
    'advanced_editing': `Access advanced editing and expansion features - upgrade to ${requiredTier} plan`,
    'embeddings': `Use semantic search and similarity features - upgrade to ${requiredTier} plan`
  }
  
  return messages[feature as keyof typeof messages] || 
    `This feature requires ${requiredTier} plan. You're currently on ${currentTier}.`
}