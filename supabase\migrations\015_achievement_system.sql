-- Achievement definitions table
CREATE TABLE IF NOT EXISTS achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  code VARCHAR(100) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  points INTEGER DEFAULT 10,
  tier VARCHAR(20) CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')) DEFAULT 'bronze',
  category VARCHAR(50) CHECK (category IN ('writing', 'streak', 'community', 'exploration', 'mastery')),
  criteria JSONB NOT NULL,
  icon VARCHAR(50),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User achievements tracking
CREATE TABLE IF NOT EXISTS user_achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
  unlocked_at TIMESTAMPTZ DEFAULT NOW(),
  progress INTEGER DEFAULT 0,
  metadata JSONB,
  UNIQUE(user_id, achievement_id)
);

-- Achievement progress tracking
CREATE TABLE IF NOT EXISTS achievement_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_code VARCHAR(100) NOT NULL,
  progress_key VARCHAR(100) NOT NULL,
  progress_value INTEGER DEFAULT 0,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, achievement_code, progress_key)
);

-- Create indexes
CREATE INDEX idx_user_achievements_user ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_unlocked ON user_achievements(unlocked_at DESC);
CREATE INDEX idx_achievement_progress_user ON achievement_progress(user_id);

-- Enable RLS
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievement_progress ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Achievements are public" ON achievements
  FOR SELECT USING (true);

CREATE POLICY "Users can view their own achievements" ON user_achievements
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert user achievements" ON user_achievements
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view their own progress" ON achievement_progress
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage achievement progress" ON achievement_progress
  FOR ALL USING (true);

-- Insert initial achievements
INSERT INTO achievements (code, name, description, points, tier, category, criteria, icon) VALUES
  -- Writing achievements
  ('first_words', 'First Words', 'Write your first 100 words', 10, 'bronze', 'writing', '{"type": "word_count", "value": 100}', 'edit'),
  ('novice_writer', 'Novice Writer', 'Write 1,000 words', 25, 'bronze', 'writing', '{"type": "word_count", "value": 1000}', 'edit-2'),
  ('apprentice_writer', 'Apprentice Writer', 'Write 10,000 words', 50, 'silver', 'writing', '{"type": "word_count", "value": 10000}', 'edit-3'),
  ('journeyman_writer', 'Journeyman Writer', 'Write 50,000 words', 100, 'gold', 'writing', '{"type": "word_count", "value": 50000}', 'book'),
  ('master_writer', 'Master Writer', 'Write 100,000 words', 200, 'platinum', 'writing', '{"type": "word_count", "value": 100000}', 'book-open'),
  
  -- Chapter achievements
  ('chapter_one', 'Chapter One', 'Complete your first chapter', 20, 'bronze', 'writing', '{"type": "chapters_completed", "value": 1}', 'bookmark'),
  ('five_chapters', 'Five Chapters', 'Complete 5 chapters', 50, 'silver', 'writing', '{"type": "chapters_completed", "value": 5}', 'bookmarks'),
  ('ten_chapters', 'Ten Chapters', 'Complete 10 chapters', 100, 'gold', 'writing', '{"type": "chapters_completed", "value": 10}', 'library'),
  
  -- Project achievements
  ('first_project', 'First Project', 'Create your first project', 15, 'bronze', 'exploration', '{"type": "projects_created", "value": 1}', 'folder-plus'),
  ('multi_project', 'Multi-Project Author', 'Create 5 projects', 40, 'silver', 'exploration', '{"type": "projects_created", "value": 5}', 'folders'),
  
  -- Series achievements
  ('series_starter', 'Series Starter', 'Create your first series', 30, 'silver', 'exploration', '{"type": "series_created", "value": 1}', 'link'),
  ('series_master', 'Series Master', 'Complete a 3-book series', 150, 'gold', 'mastery', '{"type": "series_books_completed", "value": 3}', 'link-2'),
  
  -- Streak achievements
  ('week_streak', 'Week Warrior', 'Write for 7 consecutive days', 30, 'bronze', 'streak', '{"type": "daily_streak", "value": 7}', 'calendar'),
  ('month_streak', 'Monthly Marathon', 'Write for 30 consecutive days', 100, 'gold', 'streak', '{"type": "daily_streak", "value": 30}', 'calendar-check'),
  
  -- AI usage achievements
  ('ai_explorer', 'AI Explorer', 'Use 5 different AI agents', 25, 'bronze', 'exploration', '{"type": "ai_agents_used", "value": 5}', 'cpu'),
  ('ai_master', 'AI Master', 'Generate 50,000 AI words', 75, 'gold', 'mastery', '{"type": "ai_words_generated", "value": 50000}', 'zap'),
  
  -- Community achievements
  ('collaborator', 'Collaborator', 'Invite your first team member', 20, 'bronze', 'community', '{"type": "team_members_invited", "value": 1}', 'users'),
  ('team_leader', 'Team Leader', 'Work with 3 team members', 60, 'silver', 'community', '{"type": "team_members_invited", "value": 3}', 'users-2'),
  
  -- Export achievements
  ('first_export', 'First Export', 'Export your first project', 15, 'bronze', 'exploration', '{"type": "projects_exported", "value": 1}', 'download'),
  ('export_master', 'Export Master', 'Export in all available formats', 50, 'silver', 'mastery', '{"type": "export_formats_used", "value": 5}', 'file-output')
ON CONFLICT (code) DO NOTHING;

-- Function to check and unlock achievements
CREATE OR REPLACE FUNCTION check_and_unlock_achievements(p_user_id UUID)
RETURNS TABLE (newly_unlocked UUID[]) AS $$
DECLARE
  v_newly_unlocked UUID[];
  v_achievement RECORD;
  v_current_progress INTEGER;
  v_already_unlocked BOOLEAN;
BEGIN
  v_newly_unlocked := ARRAY[]::UUID[];
  
  -- Check each achievement
  FOR v_achievement IN SELECT * FROM achievements LOOP
    -- Check if already unlocked
    SELECT EXISTS(
      SELECT 1 FROM user_achievements 
      WHERE user_id = p_user_id AND achievement_id = v_achievement.id
    ) INTO v_already_unlocked;
    
    IF NOT v_already_unlocked THEN
      -- Check criteria based on type
      CASE v_achievement.criteria->>'type'
        WHEN 'word_count' THEN
          SELECT COALESCE(SUM(actual_word_count), 0)
          FROM chapters c
          JOIN projects p ON p.id = c.project_id
          WHERE p.user_id = p_user_id
          INTO v_current_progress;
          
        WHEN 'chapters_completed' THEN
          SELECT COUNT(*)
          FROM chapters c
          JOIN projects p ON p.id = c.project_id
          WHERE p.user_id = p_user_id AND c.status = 'completed'
          INTO v_current_progress;
          
        WHEN 'projects_created' THEN
          SELECT COUNT(*)
          FROM projects
          WHERE user_id = p_user_id
          INTO v_current_progress;
          
        WHEN 'series_created' THEN
          SELECT COUNT(*)
          FROM series
          WHERE user_id = p_user_id
          INTO v_current_progress;
          
        WHEN 'ai_words_generated' THEN
          SELECT COALESCE(SUM(words_generated), 0)
          FROM ai_usage_logs
          WHERE user_id = p_user_id
          INTO v_current_progress;
          
        WHEN 'team_members_invited' THEN
          SELECT COUNT(DISTINCT invited_email)
          FROM project_invitations
          WHERE inviter_id = p_user_id AND status = 'accepted'
          INTO v_current_progress;
          
        WHEN 'projects_exported' THEN
          SELECT COALESCE(progress_value, 0)
          FROM achievement_progress
          WHERE user_id = p_user_id 
            AND achievement_code = v_achievement.code
            AND progress_key = 'exports'
          INTO v_current_progress;
          
        ELSE
          v_current_progress := 0;
      END CASE;
      
      -- Check if criteria met
      IF v_current_progress >= (v_achievement.criteria->>'value')::INTEGER THEN
        -- Unlock achievement
        INSERT INTO user_achievements (user_id, achievement_id)
        VALUES (p_user_id, v_achievement.id)
        ON CONFLICT (user_id, achievement_id) DO NOTHING;
        
        v_newly_unlocked := array_append(v_newly_unlocked, v_achievement.id);
      END IF;
    END IF;
  END LOOP;
  
  RETURN QUERY SELECT v_newly_unlocked;
END;
$$ LANGUAGE plpgsql;

-- Function to track achievement progress
CREATE OR REPLACE FUNCTION track_achievement_progress(
  p_user_id UUID,
  p_achievement_code VARCHAR,
  p_progress_key VARCHAR,
  p_increment INTEGER DEFAULT 1
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO achievement_progress (user_id, achievement_code, progress_key, progress_value)
  VALUES (p_user_id, p_achievement_code, p_progress_key, p_increment)
  ON CONFLICT (user_id, achievement_code, progress_key)
  DO UPDATE SET 
    progress_value = achievement_progress.progress_value + p_increment,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Trigger to check achievements on relevant events
CREATE OR REPLACE FUNCTION check_achievements_on_update()
RETURNS TRIGGER AS $$
BEGIN
  -- Check achievements for the user
  PERFORM check_and_unlock_achievements(
    CASE 
      WHEN TG_TABLE_NAME = 'chapters' THEN (SELECT user_id FROM projects WHERE id = NEW.project_id)
      WHEN TG_TABLE_NAME = 'projects' THEN NEW.user_id
      WHEN TG_TABLE_NAME = 'ai_usage_logs' THEN NEW.user_id
      ELSE NULL
    END
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER check_achievements_on_chapter_update
  AFTER INSERT OR UPDATE ON chapters
  FOR EACH ROW
  EXECUTE FUNCTION check_achievements_on_update();

CREATE TRIGGER check_achievements_on_project_create
  AFTER INSERT ON projects
  FOR EACH ROW
  EXECUTE FUNCTION check_achievements_on_update();

CREATE TRIGGER check_achievements_on_ai_usage
  AFTER INSERT ON ai_usage_logs
  FOR EACH ROW
  EXECUTE FUNCTION check_achievements_on_update();