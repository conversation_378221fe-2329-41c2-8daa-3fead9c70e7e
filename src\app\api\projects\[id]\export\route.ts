import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { exportService } from '@/lib/export/export-service';
import type { ExportOptions } from '@/lib/export/export-service';
import { authenticateUserForProject } from '@/lib/api/auth-helpers';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/services/logger'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params;
    
    // Check authentication and project access
    const { user, error: authError } = await authenticateUserForProject(projectId);
    if (authError) return authError;
    
    const options: ExportOptions = await request.json();

    // Validate required fields
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    if (!options.format) {
      return NextResponse.json({ error: 'Export format is required' }, { status: 400 });
    }
    
    // Check subscription tier for export format restrictions
    const supabase = createClient()
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('tier')
      .eq('user_id', user!.id)
      .eq('status', 'active')
      .single()
    
    const userTier = subscription?.tier || 'free'
    
    // Enforce export format restrictions by tier
    const restrictedFormats = {
      pdf: ['professional', 'studio'],
      epub: ['professional', 'studio'],
      docx: ['starter', 'professional', 'studio']
    }
    
    if (restrictedFormats[options.format as keyof typeof restrictedFormats]) {
      const allowedTiers = restrictedFormats[options.format as keyof typeof restrictedFormats]
      if (!allowedTiers.includes(userTier)) {
        return NextResponse.json({ 
          error: `${options.format.toUpperCase()} export requires ${allowedTiers.join(' or ')} plan`,
          requiredTier: allowedTiers[0]
        }, { status: 403 });
      }
    }

    // Export the project
    const blob = await exportService.exportProject(projectId, options);
    
    // Track export achievement
    try {
      // Track first export achievement
      await supabase.rpc('track_achievement_progress', {
        p_user_id: user!.id,
        p_achievement_code: 'first_export',
        p_progress_key: 'exports',
        p_increment: 1
      })
      
      // Track export format for export master achievement
      await supabase.rpc('track_achievement_progress', {
        p_user_id: user!.id,
        p_achievement_code: 'export_master',
        p_progress_key: options.format,
        p_increment: 1
      })
      
      // Check for newly unlocked achievements
      await supabase.rpc('check_and_unlock_achievements', {
        p_user_id: user!.id
      })
    } catch (achievementError) {
      logger.warn('Failed to track export achievement:', achievementError)
      // Don't fail the export if achievement tracking fails
    }

    // Determine content type and filename extension
    let contentType: string;
    let fileExtension: string;

    switch (options.format) {
      case 'pdf':
        contentType = 'application/pdf';
        fileExtension = 'pdf';
        break;
      case 'docx':
        contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        fileExtension = 'docx';
        break;
      case 'epub':
        contentType = 'application/epub+zip';
        fileExtension = 'epub';
        break;
      case 'txt':
        contentType = 'text/plain';
        fileExtension = 'txt';
        break;
      case 'markdown':
        contentType = 'text/markdown';
        fileExtension = 'md';
        break;
      default:
        return NextResponse.json({ error: 'Unsupported format' }, { status: 400 });
    }

    // Convert blob to buffer for response
    const buffer = await blob.arrayBuffer();

    // Return the file
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="export.${fileExtension}"`,
        'Content-Length': buffer.byteLength.toString(),
      },
    });

  } catch (error) {
    logger.error('Export error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json({ 
        error: 'Export failed', 
        details: error.message 
      }, { status: 500 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}