-- Migration: Create missing tables for notifications, writing sessions, collaborators, etc.

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}',
  read BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for notifications
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at DESC);

-- Writing sessions table
CREATE TABLE IF NOT EXISTS writing_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES chapters(id) ON DELETE SET NULL,
  word_count INTEGER NOT NULL DEFAULT 0,
  duration INTEGER NOT NULL DEFAULT 0, -- Duration in seconds
  started_at TIMESTAMPTZ NOT NULL,
  ended_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for writing sessions
CREATE INDEX idx_writing_sessions_user_id ON writing_sessions(user_id);
CREATE INDEX idx_writing_sessions_project_id ON writing_sessions(project_id);
CREATE INDEX idx_writing_sessions_started_at ON writing_sessions(started_at DESC);

-- Project collaborators table
CREATE TABLE IF NOT EXISTS project_collaborators (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  user_email TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('viewer', 'commenter', 'editor', 'admin')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
  invited_by UUID NOT NULL REFERENCES auth.users(id),
  invitation_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(project_id, user_email)
);

-- Indexes for project collaborators
CREATE INDEX idx_project_collaborators_project_id ON project_collaborators(project_id);
CREATE INDEX idx_project_collaborators_user_id ON project_collaborators(user_id);
CREATE INDEX idx_project_collaborators_user_email ON project_collaborators(user_email);
CREATE INDEX idx_project_collaborators_status ON project_collaborators(status);

-- Processing tasks table
CREATE TABLE IF NOT EXISTS processing_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  task_type TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  priority INTEGER DEFAULT 0,
  input_data JSONB DEFAULT '{}',
  output_data JSONB,
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for processing tasks
CREATE INDEX idx_processing_tasks_user_id ON processing_tasks(user_id);
CREATE INDEX idx_processing_tasks_project_id ON processing_tasks(project_id);
CREATE INDEX idx_processing_tasks_status ON processing_tasks(status);
CREATE INDEX idx_processing_tasks_created_at ON processing_tasks(created_at DESC);

-- Task progress table (for tracking processing task progress)
CREATE TABLE IF NOT EXISTS task_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES processing_tasks(id) ON DELETE CASCADE,
  progress INTEGER NOT NULL DEFAULT 0,
  message TEXT,
  details JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index for task progress
CREATE INDEX idx_task_progress_task_id ON task_progress(task_id);

-- Functions for word count management
CREATE OR REPLACE FUNCTION increment_word_count(project_id UUID, words INTEGER)
RETURNS INTEGER AS $$
DECLARE
  new_total INTEGER;
BEGIN
  UPDATE projects
  SET total_word_count = COALESCE(total_word_count, 0) + words
  WHERE id = project_id
  RETURNING total_word_count INTO new_total;
  
  RETURN new_total;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION decrement_word_count(project_id UUID, words INTEGER)
RETURNS INTEGER AS $$
DECLARE
  new_total INTEGER;
BEGIN
  UPDATE projects
  SET total_word_count = GREATEST(0, COALESCE(total_word_count, 0) - words)
  WHERE id = project_id
  RETURNING total_word_count INTO new_total;
  
  RETURN new_total;
END;
$$ LANGUAGE plpgsql;

-- Update triggers
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_notifications_updated_at
  BEFORE UPDATE ON notifications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_writing_sessions_updated_at
  BEFORE UPDATE ON writing_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_project_collaborators_updated_at
  BEFORE UPDATE ON project_collaborators
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_processing_tasks_updated_at
  BEFORE UPDATE ON processing_tasks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Row Level Security (RLS) Policies

-- Notifications RLS
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notifications"
  ON notifications FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications"
  ON notifications FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notifications"
  ON notifications FOR DELETE
  USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications for users"
  ON notifications FOR INSERT
  WITH CHECK (true);

-- Writing sessions RLS
ALTER TABLE writing_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own writing sessions"
  ON writing_sessions FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own writing sessions"
  ON writing_sessions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own writing sessions"
  ON writing_sessions FOR DELETE
  USING (auth.uid() = user_id);

-- Project collaborators RLS
ALTER TABLE project_collaborators ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Project owners can manage collaborators"
  ON project_collaborators FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM projects
      WHERE projects.id = project_collaborators.project_id
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view collaborations they're part of"
  ON project_collaborators FOR SELECT
  USING (
    user_email = auth.jwt()->>'email'
    OR user_id = auth.uid()
  );

CREATE POLICY "Invited users can update their invitation status"
  ON project_collaborators FOR UPDATE
  USING (user_email = auth.jwt()->>'email')
  WITH CHECK (user_email = auth.jwt()->>'email');

-- Processing tasks RLS
ALTER TABLE processing_tasks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own processing tasks"
  ON processing_tasks FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own processing tasks"
  ON processing_tasks FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own processing tasks"
  ON processing_tasks FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own processing tasks"
  ON processing_tasks FOR DELETE
  USING (auth.uid() = user_id);

-- Task progress RLS
ALTER TABLE task_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view progress for their tasks"
  ON task_progress FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM processing_tasks
      WHERE processing_tasks.id = task_progress.task_id
      AND processing_tasks.user_id = auth.uid()
    )
  );

-- AI Suggestions table
CREATE TABLE IF NOT EXISTS ai_suggestions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
  suggestion_type TEXT NOT NULL CHECK (suggestion_type IN ('plot', 'character', 'dialogue', 'description', 'pacing', 'style', 'general')),
  content TEXT NOT NULL,
  context JSONB DEFAULT '{}',
  accepted BOOLEAN DEFAULT NULL,
  applied_at TIMESTAMPTZ,
  feedback TEXT,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for ai_suggestions
CREATE INDEX idx_ai_suggestions_user_id ON ai_suggestions(user_id);
CREATE INDEX idx_ai_suggestions_project_id ON ai_suggestions(project_id);
CREATE INDEX idx_ai_suggestions_chapter_id ON ai_suggestions(chapter_id);
CREATE INDEX idx_ai_suggestions_type ON ai_suggestions(suggestion_type);
CREATE INDEX idx_ai_suggestions_accepted ON ai_suggestions(accepted);
CREATE INDEX idx_ai_suggestions_created_at ON ai_suggestions(created_at DESC);

-- AI Suggestions RLS
ALTER TABLE ai_suggestions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own AI suggestions"
  ON ai_suggestions FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own AI suggestions"
  ON ai_suggestions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own AI suggestions"
  ON ai_suggestions FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own AI suggestions"
  ON ai_suggestions FOR DELETE
  USING (auth.uid() = user_id);

-- Update trigger for ai_suggestions
CREATE TRIGGER update_ai_suggestions_updated_at
  BEFORE UPDATE ON ai_suggestions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Grant necessary permissions
GRANT ALL ON notifications TO authenticated;
GRANT ALL ON writing_sessions TO authenticated;
GRANT ALL ON project_collaborators TO authenticated;
GRANT ALL ON processing_tasks TO authenticated;
GRANT ALL ON task_progress TO authenticated;
GRANT ALL ON ai_suggestions TO authenticated;
GRANT EXECUTE ON FUNCTION increment_word_count TO authenticated;
GRANT EXECUTE ON FUNCTION decrement_word_count TO authenticated;