-- Create writing_goals table
CREATE TABLE IF NOT EXISTS writing_goals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  goal_type TEXT NOT NULL CHECK (goal_type IN ('daily', 'weekly', 'monthly', 'project')),
  target_words INTEGER NOT NULL CHECK (target_words > 0),
  start_date DATE NOT NULL,
  end_date DATE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  
  -- Ensure only one active goal per type per user (or per project if project-specific)
  CONSTRAINT unique_active_goal UNIQUE NULLS NOT DISTINCT (user_id, project_id, goal_type, is_active)
);

-- Create index for faster queries
CREATE INDEX idx_writing_goals_user_active ON writing_goals(user_id, is_active);
CREATE INDEX idx_writing_goals_project ON writing_goals(project_id) WHERE project_id IS NOT NULL;
CREATE INDEX idx_writing_goals_dates ON writing_goals(start_date, end_date);

-- Enable RLS
ALTER TABLE writing_goals ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own goals" ON writing_goals
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own goals" ON writing_goals
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own goals" ON writing_goals
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own goals" ON writing_goals
  FOR DELETE USING (auth.uid() = user_id);

-- Create writing_goal_progress table for tracking daily progress
CREATE TABLE IF NOT EXISTS writing_goal_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  goal_id UUID NOT NULL REFERENCES writing_goals(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  words_written INTEGER NOT NULL DEFAULT 0,
  sessions_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  
  -- One progress entry per goal per day
  CONSTRAINT unique_goal_date UNIQUE (goal_id, date)
);

-- Create index for faster queries
CREATE INDEX idx_writing_goal_progress_goal ON writing_goal_progress(goal_id);
CREATE INDEX idx_writing_goal_progress_date ON writing_goal_progress(date);

-- Enable RLS
ALTER TABLE writing_goal_progress ENABLE ROW LEVEL SECURITY;

-- RLS Policies (inherit from parent goal)
CREATE POLICY "Users can view progress for their goals" ON writing_goal_progress
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM writing_goals 
      WHERE writing_goals.id = writing_goal_progress.goal_id 
      AND writing_goals.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create progress for their goals" ON writing_goal_progress
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM writing_goals 
      WHERE writing_goals.id = writing_goal_progress.goal_id 
      AND writing_goals.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update progress for their goals" ON writing_goal_progress
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM writing_goals 
      WHERE writing_goals.id = writing_goal_progress.goal_id 
      AND writing_goals.user_id = auth.uid()
    )
  );

-- Function to update goal progress when a writing session is created
CREATE OR REPLACE FUNCTION update_goal_progress()
RETURNS TRIGGER AS $$
DECLARE
  active_goals RECORD;
  session_date DATE;
BEGIN
  -- Get the date of the writing session
  session_date := DATE(NEW.started_at);
  
  -- Find all active goals for this user and project
  FOR active_goals IN 
    SELECT id FROM writing_goals 
    WHERE user_id = NEW.user_id 
    AND is_active = true
    AND start_date <= session_date
    AND (end_date IS NULL OR end_date >= session_date)
    AND (project_id IS NULL OR project_id = NEW.project_id)
  LOOP
    -- Insert or update progress for this goal and date
    INSERT INTO writing_goal_progress (goal_id, date, words_written, sessions_count)
    VALUES (active_goals.id, session_date, NEW.word_count, 1)
    ON CONFLICT (goal_id, date) 
    DO UPDATE SET 
      words_written = writing_goal_progress.words_written + NEW.word_count,
      sessions_count = writing_goal_progress.sessions_count + 1,
      updated_at = timezone('utc'::text, now());
  END LOOP;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update goal progress
CREATE TRIGGER update_goal_progress_on_session
  AFTER INSERT ON writing_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_goal_progress();

-- Updated timestamp trigger
CREATE TRIGGER update_writing_goals_updated_at BEFORE UPDATE ON writing_goals
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_writing_goal_progress_updated_at BEFORE UPDATE ON writing_goal_progress
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();