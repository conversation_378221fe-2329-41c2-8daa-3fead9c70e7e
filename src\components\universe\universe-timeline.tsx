'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Calendar, Plus, Trash2, Edit2, Clock, Zap, Globe, Users, Book } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export interface TimelineEvent {
  id?: string
  universe_id?: string
  event_name: string
  description: string
  event_date?: string
  relative_date?: string
  event_type: 'historical' | 'cataclysm' | 'political' | 'discovery' | 'character' | 'other'
  importance: 'universe-changing' | 'major' | 'minor' | 'personal'
  affected_series?: string[]
}

const EVENT_TYPE_ICONS = {
  historical: Clock,
  cataclysm: Zap,
  political: Globe,
  discovery: Book,
  character: Users,
  other: Calendar
}

const IMPORTANCE_COLORS = {
  'universe-changing': 'destructive',
  'major': 'default',
  'minor': 'secondary',
  'personal': 'outline'
} as const

interface UniverseTimelineProps {
  universeId: string
  events: TimelineEvent[]
  onEventsChange?: (events: TimelineEvent[]) => void
  readOnly?: boolean
}

export function UniverseTimeline({ universeId, events, onEventsChange, readOnly = false }: UniverseTimelineProps) {
  const [isAddingEvent, setIsAddingEvent] = useState(false)
  const [editingEvent, setEditingEvent] = useState<TimelineEvent | null>(null)
  const [newEvent, setNewEvent] = useState<Partial<TimelineEvent>>({
    event_type: 'historical',
    importance: 'minor'
  })
  const { toast } = useToast()

  const sortedEvents = [...events].sort((a, b) => {
    // Sort by date if available, otherwise by importance
    if (a.event_date && b.event_date) {
      return new Date(a.event_date).getTime() - new Date(b.event_date).getTime()
    }
    return 0
  })

  const handleAddEvent = async () => {
    if (!newEvent.event_name?.trim() || !newEvent.description?.trim()) {
      toast({
        title: 'Missing Information',
        description: 'Please provide both event name and description',
        variant: 'destructive'
      })
      return
    }

    try {
      const response = await fetch('/api/universes/timeline-events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          universe_id: universeId,
          ...newEvent
        })
      })

      if (!response.ok) throw new Error('Failed to add event')

      const { event } = await response.json()
      
      if (onEventsChange) {
        onEventsChange([...events, event])
      }

      setNewEvent({ event_type: 'historical', importance: 'minor' })
      setIsAddingEvent(false)
      
      toast({
        title: 'Event Added',
        description: 'Timeline event has been added successfully'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add timeline event',
        variant: 'destructive'
      })
    }
  }

  const handleUpdateEvent = async (event: TimelineEvent) => {
    try {
      const response = await fetch(`/api/universes/timeline-events`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event)
      })

      if (!response.ok) throw new Error('Failed to update event')

      if (onEventsChange) {
        onEventsChange(events.map(e => e.id === event.id ? event : e))
      }

      setEditingEvent(null)
      
      toast({
        title: 'Event Updated',
        description: 'Timeline event has been updated successfully'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update timeline event',
        variant: 'destructive'
      })
    }
  }

  const handleDeleteEvent = async (eventId: string) => {
    try {
      const response = await fetch(`/api/universes/timeline-events`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: eventId })
      })

      if (!response.ok) throw new Error('Failed to delete event')

      if (onEventsChange) {
        onEventsChange(events.filter(e => e.id !== eventId))
      }
      
      toast({
        title: 'Event Deleted',
        description: 'Timeline event has been deleted'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete timeline event',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* Add Event Button */}
      {!readOnly && (
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">Universe Timeline</h3>
            <p className="text-sm text-muted-foreground">
              Major events that shape your universe
            </p>
          </div>
          <Button onClick={() => setIsAddingEvent(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Event
          </Button>
        </div>
      )}

      {/* Timeline Events */}
      <ScrollArea className="h-[600px] pr-4">
        <div className="space-y-4">
          {sortedEvents.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Calendar className="w-12 h-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground mb-4">No timeline events yet</p>
                {!readOnly && (
                  <Button variant="outline" onClick={() => setIsAddingEvent(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add First Event
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            sortedEvents.map((event) => {
              const Icon = EVENT_TYPE_ICONS[event.event_type]
              return (
                <Card key={event.id} className="relative">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-lg bg-muted">
                          <Icon className="w-5 h-5" />
                        </div>
                        <div className="space-y-1">
                          <CardTitle className="text-base">{event.event_name}</CardTitle>
                          <div className="flex items-center gap-2">
                            <Badge variant={IMPORTANCE_COLORS[event.importance]}>
                              {event.importance.replace('-', ' ')}
                            </Badge>
                            <Badge variant="outline">{event.event_type}</Badge>
                            {event.event_date && (
                              <span className="text-sm text-muted-foreground">
                                {new Date(event.event_date).toLocaleDateString()}
                              </span>
                            )}
                            {event.relative_date && (
                              <span className="text-sm text-muted-foreground">
                                {event.relative_date}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      {!readOnly && (
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => setEditingEvent(event)}
                          >
                            <Edit2 className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => event.id && handleDeleteEvent(event.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">{event.description}</p>
                  </CardContent>
                </Card>
              )
            })
          )}
        </div>
      </ScrollArea>

      {/* Add Event Dialog */}
      <Dialog open={isAddingEvent} onOpenChange={setIsAddingEvent}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Timeline Event</DialogTitle>
            <DialogDescription>
              Create a significant event in your universe's history
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>Event Name</Label>
              <Input
                placeholder="e.g., The Great Convergence"
                value={newEvent.event_name || ''}
                onChange={(e) => setNewEvent({ ...newEvent, event_name: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Event Type</Label>
                <Select
                  value={newEvent.event_type}
                  onValueChange={(value: TimelineEvent['event_type']) => 
                    setNewEvent({ ...newEvent, event_type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="historical">Historical</SelectItem>
                    <SelectItem value="cataclysm">Cataclysm</SelectItem>
                    <SelectItem value="political">Political</SelectItem>
                    <SelectItem value="discovery">Discovery</SelectItem>
                    <SelectItem value="character">Character</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>Importance</Label>
                <Select
                  value={newEvent.importance}
                  onValueChange={(value: TimelineEvent['importance']) => 
                    setNewEvent({ ...newEvent, importance: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="universe-changing">Universe Changing</SelectItem>
                    <SelectItem value="major">Major</SelectItem>
                    <SelectItem value="minor">Minor</SelectItem>
                    <SelectItem value="personal">Personal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Date (Optional)</Label>
                <Input
                  type="date"
                  value={newEvent.event_date || ''}
                  onChange={(e) => setNewEvent({ ...newEvent, event_date: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label>Relative Date (Optional)</Label>
                <Input
                  placeholder="e.g., 1000 years before Book 1"
                  value={newEvent.relative_date || ''}
                  onChange={(e) => setNewEvent({ ...newEvent, relative_date: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label>Description</Label>
              <Textarea
                placeholder="Describe what happened and its significance..."
                value={newEvent.description || ''}
                onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingEvent(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddEvent}>
              Add Event
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Event Dialog */}
      {editingEvent && (
        <Dialog open={!!editingEvent} onOpenChange={() => setEditingEvent(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Timeline Event</DialogTitle>
              <DialogDescription>
                Update the details of this timeline event
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              {/* Same form fields as add dialog but with editingEvent values */}
              <div className="space-y-2">
                <Label>Event Name</Label>
                <Input
                  value={editingEvent.event_name}
                  onChange={(e) => setEditingEvent({ ...editingEvent, event_name: e.target.value })}
                />
              </div>
              {/* ... rest of the form fields ... */}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditingEvent(null)}>
                Cancel
              </Button>
              <Button onClick={() => handleUpdateEvent(editingEvent)}>
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}