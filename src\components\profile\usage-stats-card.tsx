'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { getUserTier, type UserSubscription } from '@/lib/subscription'

interface UsageData {
  ai_words_used: number;
  projects: number;
  exports: number;
}

interface UsageStatsCardProps {
  usage: UsageData | null
  subscription: UserSubscription
}

export function UsageStatsCard({ usage, subscription }: UsageStatsCardProps) {
  const tier = getUserTier(subscription)
  
  const currentUsage = usage || {
    ai_words_used: 0,
    projects: 0,
    exports: 0
  }

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0 // Unlimited
    return Math.min((used / limit) * 100, 100)
  }


  const formatUsage = (used: number, limit: number, unit: string = '') => {
    const formattedUsed = unit === 'GB' ? used.toFixed(1) : used.toString();
    if (limit === -1) return `${formattedUsed}${unit} (Unlimited)`
    return `${formattedUsed}${unit} / ${limit}${unit}`
  }

  const getCurrentPeriod = () => {
    const now = new Date()
    return now.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Usage Stats</CardTitle>
          <Badge variant="outline">{getCurrentPeriod()}</Badge>
        </div>
        <CardDescription>
          Your current usage for this billing period
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* AI Words */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">AI Words</span>
            <span className="text-muted-foreground">
              {formatUsage(Math.round(currentUsage.ai_words_used / 1000), Math.round(tier.limits.monthlyWords / 1000), 'k')}
            </span>
          </div>
          {tier.limits.monthlyWords !== -1 && (
            <Progress 
              value={getUsagePercentage(currentUsage.ai_words_used, tier.limits.monthlyWords)}
              className="h-2"
            />
          )}
        </div>

        {/* Projects */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">Projects</span>
            <span className="text-muted-foreground">
              {formatUsage(currentUsage.projects, tier.limits.projects)}
            </span>
          </div>
          {tier.limits.projects !== -1 && (
            <Progress 
              value={getUsagePercentage(currentUsage.projects, tier.limits.projects)}
              className="h-2"
            />
          )}
        </div>


        {/* Export Usage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">Exports</span>
            <span className="text-muted-foreground">
              {currentUsage.exports} this month
            </span>
          </div>
          <div className="text-xs text-muted-foreground">
            Available formats: {tier.limits.exportFormats.join(', ')}
          </div>
        </div>

        {/* Usage Summary */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-primary">
                {Math.round(currentUsage.ai_words_used / 1000)}k
              </p>
              <p className="text-xs text-muted-foreground">Words Used</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-primary">
                {tier.limits.monthlyWords === -1 ? '∞' : 
                  Math.round(Math.max(0, tier.limits.monthlyWords - currentUsage.ai_words_used) / 1000) + 'k'}
              </p>
              <p className="text-xs text-muted-foreground">Words Remaining</p>
            </div>
          </div>
        </div>

        {/* Upgrade Notice */}
        {tier.id === 'starter' && (
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm text-center text-muted-foreground">
              Upgrade to Writer plan for more AI words and additional agents
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}