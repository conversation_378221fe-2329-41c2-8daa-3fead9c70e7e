-- Additional performance indexes for recently added features

-- Index for achievement progress tracking
CREATE INDEX IF NOT EXISTS idx_achievement_progress_user_code
ON achievement_progress (user_id, achievement_code)
INCLUDE (progress_value, updated_at);

-- Index for user achievements lookup
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_unlocked
ON user_achievements (user_id, unlocked_at DESC)
INCLUDE (achievement_id);

-- Index for project invitations
CREATE INDEX IF NOT EXISTS idx_project_invitations_token
ON project_invitations (invitation_token)
WHERE status = 'pending';

CREATE INDEX IF NOT EXISTS idx_project_invitations_project_status
ON project_invitations (project_id, status)
INCLUDE (invited_email, role);

-- Index for project collaborators
CREATE INDEX IF NOT EXISTS idx_project_collaborators_project_user
ON project_collaborators (project_id, user_id)
WHERE removed_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_project_collaborators_user_projects
ON project_collaborators (user_id, joined_at DESC)
WHERE removed_at IS NULL;

-- Index for series books relationships
CREATE INDEX IF NOT EXISTS idx_series_books_series_order
ON series_books (series_id, book_number)
INCLUDE (project_id);

-- Index for universe timeline events
CREATE INDEX IF NOT EXISTS idx_universe_timeline_events_universe_date
ON universe_timeline_events (universe_id, event_date)
INCLUDE (event_name, importance);

-- Index for voice profiles
CREATE INDEX IF NOT EXISTS idx_voice_profiles_user_project
ON voice_profiles (user_id, project_id)
WHERE is_active = true;

-- Index for writing goals
CREATE INDEX IF NOT EXISTS idx_writing_goals_user_active
ON writing_goals (user_id, is_active)
WHERE is_active = true;

-- Index for AI usage logs
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_user_date
ON ai_usage_logs (user_id, created_at DESC)
INCLUDE (model_used, tokens_used);

-- Index for subscription usage tracking
CREATE INDEX IF NOT EXISTS idx_subscription_usage_user_month
ON subscription_usage (user_id, month_start DESC)
INCLUDE (words_generated, projects_created);

-- Index for collaboration sessions
CREATE INDEX IF NOT EXISTS idx_collaboration_sessions_project_active
ON project_collaborators (project_id, last_seen_at DESC)
WHERE removed_at IS NULL AND last_seen_at > NOW() - INTERVAL '5 minutes';

-- Analyze new tables
ANALYZE achievement_progress;
ANALYZE user_achievements;
ANALYZE project_invitations;
ANALYZE project_collaborators;
ANALYZE series_books;
ANALYZE universe_timeline_events;
ANALYZE voice_profiles;
ANALYZE writing_goals;
ANALYZE ai_usage_logs;
ANALYZE subscription_usage;