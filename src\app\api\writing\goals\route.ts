import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'

const createGoalSchema = z.object({
  goal_type: z.enum(['daily', 'weekly', 'monthly', 'project']),
  target_words: z.number().int().positive(),
  project_id: z.string().uuid().nullable().optional(),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).nullable().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('project_id')
    const activeOnly = searchParams.get('active') === 'true'
    const includeProgress = searchParams.get('include_progress') === 'true'

    let query = supabase
      .from('writing_goals')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (projectId) {
      query = query.eq('project_id', projectId)
    }

    if (activeOnly) {
      query = query.eq('is_active', true)
    }

    const { data: goals, error } = await query

    if (error) {
      logger.error('Error fetching writing goals:', error)
      return NextResponse.json({ error: 'Failed to fetch goals' }, { status: 500 })
    }

    // Optionally include progress data
    if (includeProgress && goals && goals.length > 0) {
      const goalIds = goals.map(g => g.id)
      
      // Get progress for the last 30 days
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      const { data: progress } = await supabase
        .from('writing_goal_progress')
        .select('*')
        .in('goal_id', goalIds)
        .gte('date', thirtyDaysAgo.toISOString().split('T')[0])
        .order('date', { ascending: false })

      // Group progress by goal_id
      const progressByGoal = progress?.reduce((acc, p) => {
        if (!acc[p.goal_id]) acc[p.goal_id] = []
        acc[p.goal_id].push(p)
        return acc
      }, {} as Record<string, typeof progress>)

      // Add progress to goals
      const goalsWithProgress = goals.map(goal => ({
        ...goal,
        progress: progressByGoal?.[goal.id] || []
      }))

      return NextResponse.json({ goals: goalsWithProgress })
    }

    return NextResponse.json({ goals })
  } catch (error) {
    logger.error('Error in writing goals GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const goalData = createGoalSchema.parse(body)

    // Deactivate existing goals of the same type if creating a new active goal
    if (body.is_active !== false) {
      const deactivateQuery = supabase
        .from('writing_goals')
        .update({ is_active: false })
        .eq('user_id', user.id)
        .eq('goal_type', goalData.goal_type)
        .eq('is_active', true)

      if (goalData.project_id) {
        deactivateQuery.eq('project_id', goalData.project_id)
      } else {
        deactivateQuery.is('project_id', null)
      }

      await deactivateQuery
    }

    // Create the new goal
    const { data: goal, error } = await supabase
      .from('writing_goals')
      .insert({
        ...goalData,
        user_id: user.id,
        is_active: body.is_active !== false,
      })
      .select()
      .single()

    if (error) {
      logger.error('Error creating writing goal:', error)
      return NextResponse.json({ error: 'Failed to create goal' }, { status: 500 })
    }

    // Initialize today's progress if it's a new active goal
    if (goal.is_active) {
      const today = new Date().toISOString().split('T')[0]
      
      await supabase
        .from('writing_goal_progress')
        .insert({
          goal_id: goal.id,
          date: today,
          words_written: 0,
          sessions_count: 0,
        })
        .select()
    }

    return NextResponse.json({ goal }, { status: 201 })
  } catch (error) {
    logger.error('Error in writing goals POST:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 })
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}