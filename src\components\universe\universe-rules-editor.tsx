'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Trash2, Edit2, Save, X } from 'lucide-react'

export interface UniverseRule {
  id: string
  category: string
  title: string
  description: string
}

const RULE_CATEGORIES = [
  'Magic System',
  'Technology',
  'Physics',
  'Society',
  'History',
  'Geography',
  'Biology',
  'Politics',
  'Religion',
  'Other'
]

interface UniverseRulesEditorProps {
  rules: UniverseRule[]
  onRulesChange: (rules: UniverseRule[]) => void
  readOnly?: boolean
}

export function UniverseRulesEditor({ rules, onRulesChange, readOnly = false }: UniverseRulesEditorProps) {
  const [editingRule, setEditingRule] = useState<string | null>(null)
  const [newRule, setNewRule] = useState<Partial<UniverseRule>>({
    category: 'Other',
    title: '',
    description: ''
  })

  const handleAddRule = () => {
    if (!newRule.title?.trim() || !newRule.description?.trim()) return

    const rule: UniverseRule = {
      id: `rule-${Date.now()}`,
      category: newRule.category || 'Other',
      title: newRule.title.trim(),
      description: newRule.description.trim()
    }

    onRulesChange([...rules, rule])
    setNewRule({ category: 'Other', title: '', description: '' })
  }

  const handleUpdateRule = (id: string, updates: Partial<UniverseRule>) => {
    onRulesChange(
      rules.map(rule => 
        rule.id === id 
          ? { ...rule, ...updates }
          : rule
      )
    )
    setEditingRule(null)
  }

  const handleDeleteRule = (id: string) => {
    onRulesChange(rules.filter(rule => rule.id !== id))
  }

  const groupedRules = rules.reduce((acc, rule) => {
    const category = rule.category || 'Other'
    if (!acc[category]) acc[category] = []
    acc[category].push(rule)
    return acc
  }, {} as Record<string, UniverseRule[]>)

  return (
    <div className="space-y-6">
      {!readOnly && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Add Universe Rule</CardTitle>
            <CardDescription>Define the fundamental rules that govern your universe</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Category</Label>
                <Select 
                  value={newRule.category} 
                  onValueChange={(value) => setNewRule({ ...newRule, category: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {RULE_CATEGORIES.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>Rule Title</Label>
                <Input
                  placeholder="e.g., Conservation of Magical Energy"
                  value={newRule.title || ''}
                  onChange={(e) => setNewRule({ ...newRule, title: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label>Description</Label>
              <Textarea
                placeholder="Describe how this rule works in your universe..."
                value={newRule.description || ''}
                onChange={(e) => setNewRule({ ...newRule, description: e.target.value })}
                rows={3}
              />
            </div>
            <Button 
              onClick={handleAddRule}
              disabled={!newRule.title?.trim() || !newRule.description?.trim()}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Rule
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Rules by Category */}
      {Object.entries(groupedRules).map(([category, categoryRules]) => (
        <Card key={category}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">{category}</CardTitle>
              <Badge variant="secondary">{categoryRules.length} rules</Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            {categoryRules.map((rule) => (
              <div 
                key={rule.id} 
                className="p-4 border rounded-lg space-y-2 hover:bg-muted/50 transition-colors"
              >
                {editingRule === rule.id ? (
                  <div className="space-y-3">
                    <Input
                      value={rule.title}
                      onChange={(e) => handleUpdateRule(rule.id, { title: e.target.value })}
                      className="font-medium"
                    />
                    <Textarea
                      value={rule.description}
                      onChange={(e) => handleUpdateRule(rule.id, { description: e.target.value })}
                      rows={3}
                    />
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        onClick={() => setEditingRule(null)}
                      >
                        <Save className="w-4 h-4 mr-1" />
                        Save
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => setEditingRule(null)}
                      >
                        <X className="w-4 h-4 mr-1" />
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex items-start justify-between">
                      <h4 className="font-medium">{rule.title}</h4>
                      {!readOnly && (
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => setEditingRule(rule.id)}
                          >
                            <Edit2 className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDeleteRule(rule.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">{rule.description}</p>
                  </>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      ))}

      {rules.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <p className="text-muted-foreground mb-4">No universe rules defined yet</p>
            {!readOnly && (
              <Button 
                variant="outline"
                onClick={() => document.querySelector<HTMLInputElement>('input[placeholder*="Rule Title"]')?.focus()}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Rule
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}