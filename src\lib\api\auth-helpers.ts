import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import type { User } from '@supabase/supabase-js';
import { requireAuth, requireAdmin, requireProjectAccess, extractProjectId } from './auth-middleware';

interface AuthResult {
  user: User | null;
  error?: NextResponse;
}

interface ProjectAuthResult extends AuthResult {
  projectId?: string;
}

/**
 * Authenticates a user for an API route
 * @returns User object if authenticated, or an error response
 */
export async function authenticateUser(): Promise<AuthResult> {
  const supabase = await createServerSupabaseClient();
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    return {
      user: null,
      error: NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    };
  }
  
  return { user };
}

/**
 * Authenticates a user and verifies they have access to a specific project
 * @param projectId - The project ID to check access for
 * @returns User object and projectId if authenticated and authorized, or an error response
 */
export async function authenticateUserForProject(projectId: string | null): Promise<ProjectAuthResult> {
  if (!projectId) {
    return {
      user: null,
      error: NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      )
    };
  }

  const { user, error: authError } = await authenticateUser();
  if (authError) {
    return { user: null, error: authError };
  }

  // Verify user owns the project
  const supabase = await createServerSupabaseClient();
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .select('id')
    .eq('id', projectId)
    .eq('user_id', user!.id)
    .single();

  if (projectError || !project) {
    return {
      user: null,
      error: NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    };
  }

  return { user, projectId };
}

/**
 * Authenticates a user and verifies they have access to a specific series
 * @param seriesId - The series ID to check access for
 * @returns User object and seriesId if authenticated and authorized, or an error response
 */
export async function authenticateUserForSeries(seriesId: string | null): Promise<ProjectAuthResult> {
  if (!seriesId) {
    return {
      user: null,
      error: NextResponse.json(
        { error: 'Series ID is required' },
        { status: 400 }
      )
    };
  }

  const { user, error: authError } = await authenticateUser();
  if (authError) {
    return { user: null, error: authError };
  }

  // Verify user owns the series through a project
  const supabase = await createServerSupabaseClient();
  const { data: seriesBooks, error: seriesError } = await supabase
    .from('series_books')
    .select(`
      series_id,
      project:projects!inner(
        id,
        user_id
      )
    `)
    .eq('series_id', seriesId)
    .eq('project.user_id', user!.id)
    .limit(1);

  if (seriesError || !seriesBooks || seriesBooks.length === 0) {
    return {
      user: null,
      error: NextResponse.json(
        { error: 'Series not found or access denied' },
        { status: 404 }
      )
    };
  }

  return { user, projectId: seriesId };
}

/**
 * Authenticates an admin user
 * @returns User object if authenticated as admin, or an error response
 */
export async function authenticateAdmin(): Promise<AuthResult> {
  const { user, error: authError } = await authenticateUser();
  if (authError) {
    return { user: null, error: authError };
  }

  // Check if user is admin
  const supabase = await createServerSupabaseClient();
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user!.id)
    .single();

  if (profileError || !profile || profile.role !== 'admin') {
    return {
      user: null,
      error: NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    };
  }

  return { user };
}

/**
 * Wrap a route handler with authentication
 */
export function withAuth<T extends any[], R>(
  handler: (req: NextRequest, ...args: T) => Promise<R>
) {
  return async (req: NextRequest, ...args: T): Promise<R | NextResponse> => {
    const auth = await requireAuth(req)
    if (auth instanceof NextResponse) return auth as R
    return handler(req, ...args)
  }
}

/**
 * Wrap a route handler with admin authentication
 */
export function withAdmin<T extends any[], R>(
  handler: (req: NextRequest, ...args: T) => Promise<R>
) {
  return async (req: NextRequest, ...args: T): Promise<R | NextResponse> => {
    const auth = await requireAdmin(req)
    if (auth instanceof NextResponse) return auth as R
    return handler(req, ...args)
  }
}

/**
 * Wrap a route handler with project access check
 */
export function withProjectAccess<T extends any[], R>(
  handler: (req: NextRequest, ...args: T) => Promise<R>,
  requiredRole: 'viewer' | 'editor' | 'owner' = 'viewer'
) {
  return async (req: NextRequest, ...args: T): Promise<R | NextResponse> => {
    const projectId = extractProjectId(req.url)
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID not found in URL' },
        { status: 400 }
      ) as R
    }
    
    const auth = await requireProjectAccess(req, projectId, requiredRole)
    if (auth instanceof NextResponse) return auth as R
    return handler(req, ...args)
  }
}