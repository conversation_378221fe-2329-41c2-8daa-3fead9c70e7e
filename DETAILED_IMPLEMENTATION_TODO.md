# BookScribe Detailed Implementation TODO

## Overview
This is a comprehensive, detailed breakdown of all remaining implementation tasks with code snippets and specific requirements.

## Phase 3: Type Safety & Error Handling

### 3.1 Replace 'any' Types (87 instances found)

#### 3.1.1 User Types in Auth Contexts
- [ ] Update `/src/app/(dashboard)/projects/[id]/team/page.tsx`:
  ```typescript
  // Line 27: const [user, setUser] = useState<any>(null)
  // Replace with:
  import { User } from '@supabase/supabase-js'
  const [user, setUser] = useState<User | null>(null)
  
  // Line 28: const [subscription, setSubscription] = useState<any>(null)
  // Replace with:
  interface UserSubscription {
    id: string
    user_id: string
    tier: 'free' | 'starter' | 'professional' | 'studio'
    status: 'active' | 'canceled' | 'past_due'
    current_period_end: string
    // ... other fields
  }
  const [subscription, setSubscription] = useState<UserSubscription | null>(null)
  ```

#### 3.1.2 API Response Types
- [ ] Create `/src/types/api-responses.ts`:
  ```typescript
  export interface ApiResponse<T> {
    data?: T
    error?: string
    message?: string
  }
  
  export interface PaginatedResponse<T> {
    data: T[]
    total: number
    page: number
    pageSize: number
  }
  
  export interface TeamMemberResponse {
    members: TeamMember[]
    pendingInvites: string[]
  }
  ```

#### 3.1.3 Event Handler Types
- [ ] Update all onChange, onClick handlers:
  ```typescript
  // Instead of: onChange: (value: any) => void
  onChange: (value: string) => void
  onClick: (event: React.MouseEvent<HTMLButtonElement>) => void
  onSubmit: (data: FormData) => void
  ```

#### 3.1.4 Window/Global Types
- [ ] Create `/src/types/global.d.ts`:
  ```typescript
  interface Window {
    typingTimeout?: NodeJS.Timeout
    collaborationWorker?: Worker
    __REDUX_DEVTOOLS_EXTENSION__?: any
  }
  ```

#### 3.1.5 Supabase Query Types
- [ ] Update all Supabase queries to use generated types:
  ```typescript
  import { Database } from '@/lib/supabase/types'
  
  type Project = Database['public']['Tables']['projects']['Row']
  type Chapter = Database['public']['Tables']['chapters']['Row']
  type Profile = Database['public']['Tables']['profiles']['Row']
  ```

### 3.2 Error Boundaries

#### 3.2.1 Global Error Boundary
- [ ] Update `/src/app/global-error.tsx`:
  ```typescript
  'use client'
  
  import { useEffect } from 'react'
  import { Button } from '@/components/ui/button'
  import { logger } from '@/lib/services/logger'
  
  export default function GlobalError({
    error,
    reset,
  }: {
    error: Error & { digest?: string }
    reset: () => void
  }) {
    useEffect(() => {
      logger.error('Global error boundary caught error', error)
    }, [error])
    
    return (
      <html>
        <body>
          <div className="min-h-screen flex items-center justify-center">
            <div className="text-center space-y-4">
              <h2 className="text-2xl font-bold">Something went wrong!</h2>
              <p className="text-muted-foreground">
                We apologize for the inconvenience. Please try again.
              </p>
              <Button onClick={reset}>Try again</Button>
            </div>
          </div>
        </body>
      </html>
    )
  }
  ```

#### 3.2.2 Component Error Boundaries
- [ ] Create `/src/components/error-boundary/editor-error-boundary.tsx`
- [ ] Create `/src/components/error-boundary/ai-generation-error-boundary.tsx`
- [ ] Create `/src/components/error-boundary/payment-error-boundary.tsx`
- [ ] Create `/src/components/error-boundary/collaboration-error-boundary.tsx`

### 3.3 Logger Service Updates
- [ ] Replace all console.log/error/warn with logger service:
  ```typescript
  // Find: console.log(
  // Replace: logger.info(
  
  // Find: console.error(
  // Replace: logger.error(
  
  // Find: console.warn(
  // Replace: logger.warn(
  ```

## Phase 4: Complete Missing Features

### 4.1 Stripe Integration

#### 4.1.1 Webhook Handler
- [ ] Complete `/src/app/api/webhooks/stripe/route.ts`:
  ```typescript
  import { NextRequest, NextResponse } from 'next/server'
  import { headers } from 'next/headers'
  import Stripe from 'stripe'
  import { createClient } from '@/lib/supabase/server'
  import { logger } from '@/lib/services/logger'
  
  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: '2023-10-16'
  })
  
  export async function POST(req: NextRequest) {
    const body = await req.text()
    const signature = headers().get('stripe-signature')!
    
    let event: Stripe.Event
    
    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      )
    } catch (err) {
      logger.error('Stripe webhook signature verification failed', err)
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }
    
    const supabase = createClient()
    
    switch (event.type) {
      case 'checkout.session.completed':
        // Handle successful checkout
        const session = event.data.object as Stripe.Checkout.Session
        await handleCheckoutComplete(session, supabase)
        break
        
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        // Handle subscription lifecycle
        const subscription = event.data.object as Stripe.Subscription
        await handleSubscriptionChange(subscription, supabase)
        break
        
      case 'customer.subscription.deleted':
        // Handle cancellation
        const canceledSub = event.data.object as Stripe.Subscription
        await handleSubscriptionCanceled(canceledSub, supabase)
        break
        
      case 'invoice.payment_succeeded':
        // Handle successful payment
        const invoice = event.data.object as Stripe.Invoice
        await handlePaymentSucceeded(invoice, supabase)
        break
        
      case 'invoice.payment_failed':
        // Handle failed payment
        const failedInvoice = event.data.object as Stripe.Invoice
        await handlePaymentFailed(failedInvoice, supabase)
        break
    }
    
    return NextResponse.json({ received: true })
  }
  ```

#### 4.1.2 Subscription Management Functions
- [ ] Create `/src/lib/stripe/subscription-handlers.ts`:
  ```typescript
  export async function handleCheckoutComplete(
    session: Stripe.Checkout.Session,
    supabase: SupabaseClient
  ) {
    // Update user subscription in database
    const { customer, subscription, metadata } = session
    
    await supabase
      .from('user_subscriptions')
      .upsert({
        user_id: metadata?.userId,
        stripe_customer_id: customer as string,
        stripe_subscription_id: subscription as string,
        tier: metadata?.tier,
        status: 'active',
        current_period_end: new Date(metadata?.periodEnd || Date.now())
      })
  }
  ```

#### 4.1.3 Usage-Based Billing
- [ ] Create `/src/lib/stripe/usage-billing.ts`:
  ```typescript
  export async function reportAIUsage(
    userId: string,
    words: number,
    model: string
  ) {
    // Track AI usage for billing
    const supabase = createClient()
    
    await supabase
      .from('ai_usage_logs')
      .insert({
        user_id: userId,
        words_generated: words,
        model_used: model,
        timestamp: new Date().toISOString()
      })
    
    // Check if user exceeded limits
    const { data: usage } = await supabase
      .from('subscription_usage')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (usage && usage.ai_words_used > usage.ai_words_limit) {
      // Create usage record for Stripe
      await createUsageRecord(userId, words)
    }
  }
  ```

### 4.2 Admin Dashboard

#### 4.2.1 Admin Layout
- [ ] Create `/src/app/(admin)/layout.tsx`:
  ```typescript
  import { redirect } from 'next/navigation'
  import { createClient } from '@/lib/supabase/server'
  import { AdminSidebar } from '@/components/admin/admin-sidebar'
  
  export default async function AdminLayout({
    children
  }: {
    children: React.ReactNode
  }) {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) redirect('/login')
    
    // Check admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()
    
    if (profile?.role !== 'admin') redirect('/')
    
    return (
      <div className="flex h-screen">
        <AdminSidebar />
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    )
  }
  ```

#### 4.2.2 User Management Page
- [ ] Create `/src/app/(admin)/users/page.tsx`:
  ```typescript
  // Full user management with search, filter, actions
  // - List all users with pagination
  // - Search by email/name
  // - Filter by subscription tier
  // - Suspend/unsuspend users
  // - View user details and projects
  // - Manual subscription adjustments
  ```

#### 4.2.3 System Dashboard
- [ ] Create `/src/app/(admin)/dashboard/page.tsx`:
  ```typescript
  // System health and metrics
  // - Active users count
  // - Revenue metrics
  // - AI usage statistics
  // - Error rates
  // - Performance metrics
  ```

### 4.3 Universe Management

#### 4.3.1 Universe Dashboard
- [ ] Create `/src/app/(dashboard)/universes/page.tsx`:
  ```typescript
  'use client'
  
  import { useState, useEffect } from 'react'
  import { UniverseCard } from '@/components/universe/universe-card'
  import { CreateUniverseDialog } from '@/components/universe/create-universe-dialog'
  import { Button } from '@/components/ui/button'
  import { Plus } from 'lucide-react'
  
  export default function UniversesPage() {
    const [universes, setUniverses] = useState<Universe[]>([])
    
    // Load user's universes
    // Display in grid
    // Allow creation of new universes
    // Link to universe details page
  }
  ```

#### 4.3.2 Universe API Routes
- [ ] Create `/src/app/api/universes/route.ts`:
  ```typescript
  // GET: List user's universes
  // POST: Create new universe
  
  export async function GET(req: NextRequest) {
    const auth = await requireAuth(req)
    if (auth instanceof NextResponse) return auth
    
    const supabase = createClient()
    const { data: universes } = await supabase
      .from('universes')
      .select(`
        *,
        universe_members(count),
        shared_characters(count)
      `)
      .eq('creator_id', auth.id)
    
    return NextResponse.json({ universes })
  }
  ```

#### 4.3.3 Character Sharing UI
- [ ] Create `/src/components/universe/character-sharing-panel.tsx`:
  ```typescript
  // Browse shared characters
  // Request access to characters
  // Approve/deny access requests
  // Import characters into projects
  ```

### 4.4 Voice Profile Management

#### 4.4.1 Voice Profile UI
- [ ] Complete `/src/components/voice/voice-profile-manager.tsx`:
  ```typescript
  interface VoiceProfileManagerProps {
    projectId: string
    userId: string
  }
  
  export function VoiceProfileManager({ projectId, userId }: VoiceProfileManagerProps) {
    // Voice analysis dashboard
    // Sample text input
    // Analysis results display
    // Consistency scoring
    // Voice comparison tools
  }
  ```

#### 4.4.2 Voice Analysis API
- [ ] Create `/src/app/api/voice-profiles/analyze/route.ts`:
  ```typescript
  export async function POST(req: NextRequest) {
    const auth = await requireAuth(req)
    if (auth instanceof NextResponse) return auth
    
    const { text, projectId } = await req.json()
    
    // Analyze text for voice characteristics
    const analysis = await analyzeVoiceCharacteristics(text)
    
    // Compare with existing voice profile
    const consistency = await checkVoiceConsistency(projectId, analysis)
    
    return NextResponse.json({ analysis, consistency })
  }
  ```

### 4.5 Achievements System

#### 4.5.1 Achievement Tracking
- [ ] Create `/src/lib/achievements/achievement-tracker.ts`:
  ```typescript
  export class AchievementTracker {
    async checkWordCountMilestone(userId: string, totalWords: number) {
      const milestones = [1000, 5000, 10000, 50000, 100000]
      
      for (const milestone of milestones) {
        if (totalWords >= milestone) {
          await this.unlockAchievement(userId, `words_${milestone}`)
        }
      }
    }
    
    async checkChapterCompletion(userId: string, chaptersCompleted: number) {
      // Check chapter milestones
    }
    
    async checkCollaborationAchievements(userId: string, collaborators: number) {
      // Check team achievements
    }
  }
  ```

#### 4.5.2 Achievement Gallery
- [ ] Create `/src/app/(dashboard)/achievements/page.tsx`:
  ```typescript
  export default function AchievementsPage() {
    // Display all achievements
    // Show progress for locked achievements
    // Recent unlocks
    // Leaderboards
  }
  ```

## Phase 5: Database & Performance

### 5.1 Database Optimization

#### 5.1.1 Singleton Pattern
- [ ] Update `/src/lib/supabase/client.ts`:
  ```typescript
  let browserClient: SupabaseClient<Database> | undefined
  
  export function createClient() {
    if (!browserClient) {
      browserClient = createBrowserClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )
    }
    return browserClient
  }
  ```

#### 5.1.2 Add Missing Indexes
- [ ] Create `/supabase/migrations/012_performance_indexes.sql`:
  ```sql
  -- Add indexes for common queries
  CREATE INDEX CONCURRENTLY idx_chapters_project_status 
    ON chapters(project_id, status);
  
  CREATE INDEX CONCURRENTLY idx_characters_project_active 
    ON characters(project_id, is_active);
  
  CREATE INDEX CONCURRENTLY idx_ai_usage_user_timestamp 
    ON ai_usage_logs(user_id, timestamp DESC);
  
  CREATE INDEX CONCURRENTLY idx_collaboration_session_active 
    ON collaboration_sessions(project_id, is_active);
  ```

### 5.2 Caching Implementation

#### 5.2.1 API Response Caching
- [ ] Create `/src/lib/cache/api-cache.ts`:
  ```typescript
  export class ApiCache {
    private cache = new Map<string, { data: any; expires: number }>()
    
    set(key: string, data: any, ttlSeconds: number = 300) {
      this.cache.set(key, {
        data,
        expires: Date.now() + (ttlSeconds * 1000)
      })
    }
    
    get(key: string) {
      const item = this.cache.get(key)
      if (!item) return null
      
      if (Date.now() > item.expires) {
        this.cache.delete(key)
        return null
      }
      
      return item.data
    }
  }
  ```

#### 5.2.2 User Permissions Cache
- [ ] Create `/src/lib/cache/permissions-cache.ts`:
  ```typescript
  export async function getCachedPermissions(userId: string, projectId: string) {
    const cacheKey = `permissions:${userId}:${projectId}`
    const cached = permissionsCache.get(cacheKey)
    
    if (cached) return cached
    
    const permissions = await checkProjectAccess(userId, projectId)
    permissionsCache.set(cacheKey, permissions, 300) // 5 min TTL
    
    return permissions
  }
  ```

## Phase 6: Testing

### 6.1 Unit Tests

#### 6.1.1 Auth Middleware Tests
- [ ] Create `/src/lib/api/__tests__/auth-middleware.test.ts`:
  ```typescript
  describe('Auth Middleware', () => {
    it('should reject unauthenticated requests', async () => {
      const req = new NextRequest('http://localhost/api/test')
      const result = await requireAuth(req)
      expect(result).toBeInstanceOf(NextResponse)
      expect(result.status).toBe(401)
    })
    
    it('should allow authenticated requests', async () => {
      // Mock authenticated user
      const req = createAuthenticatedRequest()
      const result = await requireAuth(req)
      expect(result).toHaveProperty('id')
      expect(result).toHaveProperty('email')
    })
  })
  ```

#### 6.1.2 Email Service Tests
- [ ] Create `/src/lib/email/__tests__/email.test.ts`
- [ ] Create `/src/lib/email/__tests__/maileroo.test.ts`
- [ ] Create `/src/lib/email/__tests__/zeruh.test.ts`

### 6.2 Integration Tests

#### 6.2.1 Collaboration Flow Tests
- [ ] Create `/e2e/collaboration.spec.ts`:
  ```typescript
  test('complete collaboration flow', async ({ page }) => {
    // Send invitation
    // Accept invitation
    // Verify access
    // Test real-time editing
  })
  ```

#### 6.2.2 Subscription Tests
- [ ] Create `/e2e/subscription.spec.ts`:
  ```typescript
  test('subscription limits enforced', async ({ page }) => {
    // Test free tier limits
    // Test professional tier (2 collaborators)
    // Test studio tier (5 collaborators)
  })
  ```

## Phase 7: Documentation

### 7.1 API Documentation
- [ ] Update `/docs/API_DOCUMENTATION.md` with all new endpoints
- [ ] Add request/response examples
- [ ] Document error codes
- [ ] Add rate limiting info

### 7.2 Deployment Guide
- [ ] Create `/docs/DEPLOYMENT_GUIDE.md`:
  ```markdown
  # Deployment Guide
  
  ## Prerequisites
  - Node.js 18+
  - Supabase project
  - Stripe account
  - Maileroo account
  - Zeruh account
  
  ## Environment Variables
  [Complete list with descriptions]
  
  ## Database Setup
  1. Run migrations in order
  2. Enable Realtime for tables
  3. Configure RLS policies
  
  ## Deployment Steps
  1. Vercel deployment
  2. Environment configuration
  3. Domain setup
  4. SSL certificates
  ```

## Implementation Priority Order

1. **Week 1**: Type Safety (Phase 3.1)
2. **Week 2**: Error Boundaries & Stripe (Phase 3.2, 4.1)
3. **Week 3**: Admin Dashboard (Phase 4.2)
4. **Week 4**: Universe & Voice Features (Phase 4.3, 4.4)
5. **Week 5**: Performance & Testing (Phase 5, 6)
6. **Week 6**: Documentation & Polish (Phase 7)

## Success Metrics

- [ ] Zero TypeScript 'any' types
- [ ] All API routes authenticated
- [ ] Stripe payments fully integrated
- [ ] Admin dashboard functional
- [ ] Universe feature complete
- [ ] Voice profiles working
- [ ] All tests passing
- [ ] Documentation complete