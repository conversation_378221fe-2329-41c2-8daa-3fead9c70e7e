import { logger } from '@/lib/services/logger'
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from 'sonner'
import {
  <PERSON>r,
  <PERSON>,
  Book<PERSON><PERSON>,
  AlertCircle,
  CheckCircle,
  XCircle,
  <PERSON>,
  Edit3,
  Mi<PERSON>,
  T<PERSON>ding<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  MessageSquare,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface CharacterState {
  book_number: number
  state: {
    physical?: string
    emotional?: string
    goals?: string
    relationships?: string
  }
}

interface RelationshipChange {
  book_number: number
  character: string
  relationship_type: string
  change: string
}

interface CharacterContinuity {
  id: string
  series_id: string
  character_name: string
  first_appearance_book: number
  last_appearance_book?: number
  status: 'active' | 'inactive' | 'deceased' | 'written_out' | 'mentioned_only'
  status_change_book?: number
  status_change_reason?: string
  voice_profile_id?: string
  character_states?: CharacterState[]
  relationship_changes?: RelationshipChange[]
}

interface CharacterTimeline {
  name: string
  appearances: {
    bookNumber: number
    bookTitle: string
    role: string
    description: string
  }[]
  firstAppearance: number
  lastAppearance: number
  totalAppearances: number
}

interface VoiceProfile {
  id: string
  name: string
  type: string
}

interface CharacterContinuityTrackerProps {
  seriesId: string
  seriesName: string
  totalBooks: number
}

export function CharacterContinuityTracker({
  seriesId,
  seriesName,
  totalBooks
}: CharacterContinuityTrackerProps) {
  const [continuityData, setContinuityData] = useState<CharacterContinuity[]>([])
  const [characterTimeline, setCharacterTimeline] = useState<Record<string, CharacterTimeline>>({})
  const [voiceProfiles, setVoiceProfiles] = useState<VoiceProfile[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedCharacter, setSelectedCharacter] = useState<string | null>(null)
  const [editingCharacter, setEditingCharacter] = useState<string | null>(null)
  const [statusReason, setStatusReason] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<CharacterContinuity['status']>('active')
  const [selectedVoiceProfile, setSelectedVoiceProfile] = useState<string>('')

  useEffect(() => {
    loadData()
  }, [seriesId])

  const loadData = async () => {
    setIsLoading(true)
    try {
      // Load character continuity
      const continuityResponse = await fetch(`/api/series/${seriesId}/character-continuity`)
      if (continuityResponse.ok) {
        const data = await continuityResponse.json()
        setContinuityData(data.continuity || [])
        setCharacterTimeline(data.characterTimeline || {})
      }

      // Load voice profiles
      const voiceResponse = await fetch(`/api/voice-profiles?type=character&seriesId=${seriesId}`)
      if (voiceResponse.ok) {
        const data = await voiceResponse.json()
        setVoiceProfiles(data.profiles || [])
      }
    } catch (error) {
      logger.error('Error loading data:', error)
      toast.error('Failed to load character continuity data')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdateStatus = async () => {
    if (!editingCharacter) return

    try {
      const response = await fetch(`/api/series/${seriesId}/character-continuity`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          characterName: editingCharacter,
          status: selectedStatus,
          lastAppearanceBook: characterTimeline[editingCharacter]?.lastAppearance,
          statusChangeReason: statusReason,
          voiceProfileId: selectedVoiceProfile || undefined
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update character status')
      }

      toast.success('Character status updated')
      setEditingCharacter(null)
      setStatusReason('')
      loadData()
    } catch (error) {
      logger.error('Error updating status:', error)
      toast.error('Failed to update character status')
    }
  }

  const getStatusIcon = (status: CharacterContinuity['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'inactive':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'deceased':
        return <Skull className="h-4 w-4 text-red-600" />
      case 'written_out':
        return <XCircle className="h-4 w-4 text-orange-600" />
      case 'mentioned_only':
        return <Ghost className="h-4 w-4 text-gray-500" />
      default:
        return <User className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: CharacterContinuity['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-300'
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case 'deceased':
        return 'bg-red-100 text-red-800 border-red-300'
      case 'written_out':
        return 'bg-orange-100 text-orange-800 border-orange-300'
      case 'mentioned_only':
        return 'bg-gray-100 text-gray-800 border-gray-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const getContinuityForCharacter = (characterName: string): CharacterContinuity | undefined => {
    return continuityData.find(c => c.character_name === characterName)
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Character Continuity Tracker
              </CardTitle>
              <CardDescription>
                Track character appearances and status across {seriesName}
              </CardDescription>
            </div>
            <Badge variant="outline">
              {Object.keys(characterTimeline).length} Characters • {totalBooks} Books
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="timeline" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="timeline">Character Timeline</TabsTrigger>
              <TabsTrigger value="status">Status Overview</TabsTrigger>
              <TabsTrigger value="voices">Voice Profiles</TabsTrigger>
            </TabsList>

            <TabsContent value="timeline" className="space-y-4">
              <ScrollArea className="h-[500px] pr-4">
                <div className="space-y-4">
                  {Object.entries(characterTimeline)
                    .sort(([, a], [, b]) => b.totalAppearances - a.totalAppearances)
                    .map(([characterName, timeline]) => {
                      const continuity = getContinuityForCharacter(characterName)
                      const status = continuity?.status || 'active'
                      
                      return (
                        <div
                          key={characterName}
                          className={cn(
                            "p-4 border rounded-lg transition-colors cursor-pointer",
                            selectedCharacter === characterName && "border-primary bg-primary/5"
                          )}
                          onClick={() => setSelectedCharacter(
                            selectedCharacter === characterName ? null : characterName
                          )}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                {getStatusIcon(status)}
                                <h4 className="font-medium text-lg">{characterName}</h4>
                                <Badge 
                                  variant="outline" 
                                  className={cn("text-xs", getStatusColor(status))}
                                >
                                  {status}
                                </Badge>
                                {continuity?.voice_profile_id && (
                                  <Badge variant="secondary" className="text-xs">
                                    <Mic className="h-3 w-3 mr-1" />
                                    Voice Profile
                                  </Badge>
                                )}
                              </div>
                              
                              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                <span>
                                  First: Book {timeline.firstAppearance}
                                </span>
                                <span>•</span>
                                <span>
                                  Last: Book {timeline.lastAppearance}
                                </span>
                                <span>•</span>
                                <span>
                                  {timeline.totalAppearances} appearances
                                </span>
                              </div>

                              {continuity?.status_change_reason && (
                                <Alert className="mt-3">
                                  <AlertCircle className="h-4 w-4" />
                                  <AlertDescription className="text-sm">
                                    {continuity.status_change_reason}
                                  </AlertDescription>
                                </Alert>
                              )}

                              {selectedCharacter === characterName && (
                                <div className="mt-4 space-y-3">
                                  <h5 className="text-sm font-medium">Appearances by Book:</h5>
                                  <div className="space-y-2">
                                    {timeline.appearances.map((appearance, idx) => (
                                      <div 
                                        key={idx}
                                        className="flex items-center gap-3 text-sm p-2 bg-muted rounded"
                                      >
                                        <BookOpen className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">
                                          Book {appearance.bookNumber}: {appearance.bookTitle}
                                        </span>
                                        <Badge variant="outline" className="text-xs">
                                          {appearance.role}
                                        </Badge>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>

                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation()
                                setEditingCharacter(characterName)
                                setSelectedStatus(status)
                                setSelectedVoiceProfile(continuity?.voice_profile_id || '')
                              }}
                            >
                              <Edit3 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      )
                    })}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="status" className="space-y-4">
              <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                {['active', 'inactive', 'deceased', 'written_out', 'mentioned_only'].map((status) => {
                  const characters = Object.entries(characterTimeline).filter(([name]) => {
                    const continuity = getContinuityForCharacter(name)
                    return (continuity?.status || 'active') === status
                  })

                  return (
                    <Card key={status}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-sm font-medium capitalize">
                            {status.replace('_', ' ')}
                          </CardTitle>
                          {getStatusIcon(status as CharacterContinuity['status'])}
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold mb-2">
                          {characters.length}
                        </div>
                        <div className="space-y-1">
                          {characters.slice(0, 3).map(([name]) => (
                            <p key={name} className="text-xs text-muted-foreground truncate">
                              {name}
                            </p>
                          ))}
                          {characters.length > 3 && (
                            <p className="text-xs text-muted-foreground">
                              +{characters.length - 3} more
                            </p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </TabsContent>

            <TabsContent value="voices" className="space-y-4">
              <div className="space-y-4">
                {Object.entries(characterTimeline).map(([characterName]) => {
                  const continuity = getContinuityForCharacter(characterName)
                  const voiceProfile = voiceProfiles.find(v => v.id === continuity?.voice_profile_id)
                  
                  return (
                    <div key={characterName} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <User className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{characterName}</p>
                          {voiceProfile ? (
                            <p className="text-sm text-muted-foreground">
                              Voice: {voiceProfile.name}
                            </p>
                          ) : (
                            <p className="text-sm text-muted-foreground">
                              No voice profile assigned
                            </p>
                          )}
                        </div>
                      </div>
                      {voiceProfile && (
                        <Badge variant="secondary">
                          <Mic className="h-3 w-3 mr-1" />
                          Configured
                        </Badge>
                      )}
                    </div>
                  )
                })}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Edit Character Status Dialog */}
      <Dialog open={!!editingCharacter} onOpenChange={() => setEditingCharacter(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Character Status</DialogTitle>
            <DialogDescription>
              Update the status and voice profile for {editingCharacter}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="status">Character Status</Label>
              <Select 
                value={selectedStatus} 
                onValueChange={(value) => setSelectedStatus(value as CharacterContinuity['status'])}
              >
                <SelectTrigger id="status">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Active
                    </div>
                  </SelectItem>
                  <SelectItem value="inactive">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-yellow-600" />
                      Inactive
                    </div>
                  </SelectItem>
                  <SelectItem value="deceased">
                    <div className="flex items-center gap-2">
                      <Skull className="h-4 w-4 text-red-600" />
                      Deceased
                    </div>
                  </SelectItem>
                  <SelectItem value="written_out">
                    <div className="flex items-center gap-2">
                      <XCircle className="h-4 w-4 text-orange-600" />
                      Written Out
                    </div>
                  </SelectItem>
                  <SelectItem value="mentioned_only">
                    <div className="flex items-center gap-2">
                      <Ghost className="h-4 w-4 text-gray-500" />
                      Mentioned Only
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="reason">Status Change Reason</Label>
              <Textarea
                id="reason"
                value={statusReason}
                onChange={(e) => setStatusReason(e.target.value)}
                placeholder="Explain why this character's status changed..."
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="voiceProfile">Voice Profile</Label>
              <Select 
                value={selectedVoiceProfile} 
                onValueChange={setSelectedVoiceProfile}
              >
                <SelectTrigger id="voiceProfile">
                  <SelectValue placeholder="Select a voice profile" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No voice profile</SelectItem>
                  {voiceProfiles.map((profile) => (
                    <SelectItem key={profile.id} value={profile.id}>
                      {profile.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setEditingCharacter(null)}
              >
                Cancel
              </Button>
              <Button onClick={handleUpdateStatus}>
                Update Status
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}