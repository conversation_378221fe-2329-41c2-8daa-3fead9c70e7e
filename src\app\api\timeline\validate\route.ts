import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { getTimelineValidator } from '@/lib/timeline/timeline-instances';
import { requireProjectAccess } from '@/lib/api/auth-middleware';
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    const { projectId } = await request.json();
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }
    
    // Check authentication and project access
    const authResult = await requireProjectAccess(request, projectId, 'viewer');
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const validator = getTimelineValidator(projectId);

    const validation = await validator.validateTimeline();
    const analysis = validator.getTimelineAnalysis();
    
    return NextResponse.json({
      success: true,
      validation,
      analysis
    });

  } catch (error) {
    logger.error('Error validating timeline:', error);
    return NextResponse.json(
      { error: 'Failed to validate timeline' },
      { status: 500 }
    );
  }
}