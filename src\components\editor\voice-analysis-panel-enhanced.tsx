import { logger } from '@/lib/services/logger'
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from 'sonner'
import {
  <PERSON><PERSON>,
  RefreshC<PERSON>,
  Al<PERSON><PERSON><PERSON>gle,
  BarChart3,
  MessageSquare,
  <PERSON>h,
  <PERSON>rkles,
  Plus,
  <PERSON>tings,
  Book<PERSON>pen,
  User,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { VoiceTrainer } from '@/components/voice/voice-trainer'

interface VoiceProfile {
  id: string
  name: string
  description?: string
  type: 'author' | 'character' | 'narrator'
  confidence: number
  training_samples_count: number
  total_words_analyzed: number
  patterns: any
  created_at: string
  updated_at: string
}

interface VoiceAnalysisPanelEnhancedProps {
  content: string
  projectId: string
  seriesId?: string
  characterId?: string
  onSuggestionApply?: (suggestion: string) => void
}

interface VoiceMetric {
  label: string
  value: number
  target: number
  unit?: string
  description?: string
}

export function VoiceAnalysisPanelEnhanced({ 
  content, 
  projectId, 
  seriesId,
  characterId,
  onSuggestionApply 
}: VoiceAnalysisPanelEnhancedProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [voiceProfiles, setVoiceProfiles] = useState<VoiceProfile[]>([])
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(null)
  const [voiceMatches, setVoiceMatches] = useState<any[]>([])
  const [consistencyScore, setConsistencyScore] = useState<number | null>(null)
  const [isProfileLoading, setIsProfileLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showTrainer, setShowTrainer] = useState(false)
  const [activeTab, setActiveTab] = useState('analysis')

  // Load voice profiles
  useEffect(() => {
    const loadVoiceProfiles = async () => {
      setIsProfileLoading(true)
      try {
        const params = new URLSearchParams()
        if (projectId) params.append('projectId', projectId)
        if (seriesId) params.append('seriesId', seriesId)
        
        const response = await fetch(`/api/voice-profiles?${params}`)
        if (response.ok) {
          const data = await response.json()
          setVoiceProfiles(data.profiles)
          
          // Auto-select first profile if available
          if (data.profiles.length > 0 && !selectedProfileId) {
            setSelectedProfileId(data.profiles[0].id)
          }
        }
      } catch (err) {
        logger.error('Error loading voice profiles:', err)
      } finally {
        setIsProfileLoading(false)
      }
    }

    loadVoiceProfiles()
  }, [projectId, seriesId, selectedProfileId])

  const selectedProfile = voiceProfiles.find(p => p.id === selectedProfileId)

  const analyzeVoice = async () => {
    if (!content || content.length < 100) {
      setError('Please write at least 100 characters to analyze voice')
      return
    }

    if (!selectedProfileId) {
      setError('Please select or create a voice profile first')
      return
    }

    setIsAnalyzing(true)
    setError(null)
    try {
      const response = await fetch('/api/analysis/voice-consistency', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          profileId: selectedProfileId,
          projectId,
          chapterId: characterId // If analyzing for a specific chapter
        })
      })

      if (!response.ok) {
        throw new Error('Failed to analyze voice')
      }

      const data = await response.json()
      setConsistencyScore(data.consistencyScore)
      setVoiceMatches(data.suggestions || [])
      
      toast.success('Voice analysis complete')
    } catch (err) {
      setError('Failed to analyze voice. Please try again.')
      logger.error('Voice analysis error:', err)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const getVoiceMetrics = (): VoiceMetric[] => {
    if (!selectedProfile) return []
    
    const patterns = selectedProfile.patterns
    return [
      {
        label: 'Sentence Complexity',
        value: patterns.sentenceStructure?.complexityScore || 0,
        target: 65,
        description: 'Variety in sentence structure'
      },
      {
        label: 'Vocabulary Formality',
        value: patterns.vocabulary?.formalityScore || 0,
        target: patterns.vocabulary?.formalityScore || 50,
        description: 'Word choice sophistication'
      },
      {
        label: 'Dialogue Ratio',
        value: patterns.style?.dialogueRatio || 0,
        target: 40,
        unit: '%',
        description: 'Dialogue vs narrative balance'
      },
      {
        label: 'Emotional Intensity',
        value: patterns.tone?.intensity || 0,
        target: patterns.tone?.intensity || 50,
        description: 'Emotional expression strength'
      },
      {
        label: 'Rhythm Variation',
        value: patterns.rhythm?.paragraphLengthVariation || 0,
        target: 70,
        description: 'Paragraph length diversity'
      }
    ]
  }

  const getMatchIcon = (aspect: string) => {
    switch (aspect) {
      case 'sentence_structure':
        return Hash
      case 'vocabulary':
        return MessageSquare
      case 'tone':
        return Sparkles
      case 'rhythm':
        return BarChart3
      default:
        return AlertTriangle
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'author':
        return <BookOpen className="h-4 w-4" />
      case 'character':
        return <User className="h-4 w-4" />
      case 'narrator':
        return <Mic className="h-4 w-4" />
      default:
        return <Mic className="h-4 w-4" />
    }
  }

  const handleProfileCreated = (profile: VoiceProfile) => {
    setVoiceProfiles([...voiceProfiles, profile])
    setSelectedProfileId(profile.id)
    setShowTrainer(false)
    setActiveTab('analysis')
    toast.success('Voice profile created successfully')
  }

  if (isProfileLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mic className="h-5 w-5" />
            Voice Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Mic className="h-5 w-5" />
              Voice Analysis
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowTrainer(true)}
              >
                <Settings className="h-4 w-4 mr-2" />
                Manage Profiles
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="analysis">Analysis</TabsTrigger>
              <TabsTrigger value="profiles">Voice Profiles</TabsTrigger>
            </TabsList>

            <TabsContent value="analysis" className="space-y-4">
              {/* Profile Selection */}
              <div>
                <label className="text-sm font-medium mb-2 block">Active Voice Profile</label>
                <Select value={selectedProfileId || ''} onValueChange={setSelectedProfileId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a voice profile" />
                  </SelectTrigger>
                  <SelectContent>
                    {voiceProfiles.map((profile) => (
                      <SelectItem key={profile.id} value={profile.id}>
                        <div className="flex items-center gap-2">
                          {getTypeIcon(profile.type)}
                          <span>{profile.name}</span>
                          <Badge variant="outline" className="ml-2">
                            {Math.round(profile.confidence * 100)}%
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {!selectedProfile ? (
                <div className="text-center py-8">
                  <Mic className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground mb-4">
                    No voice profile selected. Create or select a profile to analyze your writing.
                  </p>
                  <Button onClick={() => setShowTrainer(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Voice Profile
                  </Button>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Analyze Button */}
                  <Button
                    onClick={analyzeVoice}
                    disabled={isAnalyzing || !content}
                    className="w-full"
                  >
                    {isAnalyzing ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Analyze Voice Consistency
                      </>
                    )}
                  </Button>

                  {/* Consistency Score */}
                  {consistencyScore !== null && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Voice Consistency Score</span>
                        <span className={cn(
                          "text-lg font-bold",
                          consistencyScore >= 0.8 ? "text-green-600" :
                          consistencyScore >= 0.6 ? "text-yellow-600" :
                          "text-red-600"
                        )}>
                          {Math.round(consistencyScore * 100)}%
                        </span>
                      </div>
                      <Progress value={consistencyScore * 100} className="h-3" />
                    </div>
                  )}

                  {/* Voice Metrics */}
                  <div>
                    <h4 className="text-sm font-medium mb-3">Target Voice Metrics</h4>
                    <div className="space-y-3">
                      {getVoiceMetrics().map((metric, idx) => (
                        <div key={idx} className="space-y-1">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">{metric.label}</span>
                            <span className="font-medium">
                              {metric.value}{metric.unit || ''} / {metric.target}{metric.unit || ''}
                            </span>
                          </div>
                          <Progress 
                            value={(metric.value / metric.target) * 100} 
                            className="h-2"
                          />
                          {metric.description && (
                            <p className="text-xs text-muted-foreground">{metric.description}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Voice Suggestions */}
                  {voiceMatches.length > 0 && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="text-sm font-medium mb-3">Voice Consistency Suggestions</h4>
                        <ScrollArea className="h-[200px]">
                          <div className="space-y-3">
                            {voiceMatches.map((match, idx) => {
                              const Icon = getMatchIcon(match.voiceAspect)
                              
                              return (
                                <div 
                                  key={idx}
                                  className="p-3 rounded-lg border space-y-2"
                                >
                                  <div className="flex items-start gap-2">
                                    <Icon className="h-4 w-4 mt-0.5 text-muted-foreground" />
                                    <div className="flex-1">
                                      <p className="text-sm font-medium">{match.message}</p>
                                      {match.explanation && (
                                        <p className="text-xs text-muted-foreground mt-1">
                                          {match.explanation}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                  {match.replacement && onSuggestionApply && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => onSuggestionApply(match.replacement)}
                                      className="w-full"
                                    >
                                      Apply Suggestion
                                    </Button>
                                  )}
                                </div>
                              )
                            })}
                          </div>
                        </ScrollArea>
                      </div>
                    </>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="profiles" className="space-y-4">
              <div className="space-y-4">
                {voiceProfiles.length === 0 ? (
                  <div className="text-center py-8">
                    <Mic className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground mb-4">
                      No voice profiles created yet.
                    </p>
                    <Button onClick={() => setShowTrainer(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Your First Profile
                    </Button>
                  </div>
                ) : (
                  <>
                    <div className="flex justify-between items-center">
                      <h4 className="text-sm font-medium">Your Voice Profiles</h4>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowTrainer(true)}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        New Profile
                      </Button>
                    </div>
                    <div className="space-y-2">
                      {voiceProfiles.map((profile) => (
                        <div
                          key={profile.id}
                          className={cn(
                            "p-4 rounded-lg border cursor-pointer transition-colors",
                            selectedProfileId === profile.id 
                              ? "border-primary bg-primary/5" 
                              : "hover:bg-muted/50"
                          )}
                          onClick={() => setSelectedProfileId(profile.id)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-2">
                              {getTypeIcon(profile.type)}
                              <div>
                                <h5 className="font-medium">{profile.name}</h5>
                                {profile.description && (
                                  <p className="text-xs text-muted-foreground mt-1">
                                    {profile.description}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="text-right">
                              <Badge variant="outline">
                                {Math.round(profile.confidence * 100)}% confidence
                              </Badge>
                              <p className="text-xs text-muted-foreground mt-1">
                                {profile.training_samples_count} samples
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Voice Trainer Dialog */}
      <Dialog open={showTrainer} onOpenChange={setShowTrainer}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Voice Profile Management</DialogTitle>
          </DialogHeader>
          <VoiceTrainer
            projectId={projectId}
            seriesId={seriesId}
            characterId={characterId}
            onProfileCreated={handleProfileCreated}
            existingProfile={selectedProfile}
          />
        </DialogContent>
      </Dialog>
    </>
  )
}