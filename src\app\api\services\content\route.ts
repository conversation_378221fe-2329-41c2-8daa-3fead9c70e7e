import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import ServiceManager from '@/lib/services/service-manager';
import { authenticateUser, handleRouteError } from '@/lib/auth';
import { aiLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter';
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for AI content generation (expensive operations)
    const clientIP = getClientIP(request);
    const rateLimitResult = aiLimiter.check(15, clientIP); // 15 requests per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    // Authentication required for content generation
    const authResult = await authenticateUser();
    if (!authResult.success) {
      return authResult.response!;
    }

    const body = await request.json();
    const { action, ...params } = body;
    
    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const contentGenerator = await serviceManager.getContentGenerator();
    
    if (!contentGenerator) {
      return NextResponse.json(
        { error: 'Content Generator service not available' },
        { status: 503 }
      );
    }

    switch (action) {
      case 'generate':
        const generateResult = await serviceManager.generateContent(params);
        return NextResponse.json({ 
          success: !!generateResult, 
          content: generateResult 
        });

      case 'generate_scene_outline':
        const outlineResult = await contentGenerator.generateSceneOutline(params);
        return NextResponse.json(outlineResult);

      case 'generate_dialogue':
        const dialogueResult = await contentGenerator.generateDialogue(params);
        return NextResponse.json(dialogueResult);

      case 'generate_character':
        const characterResult = await contentGenerator.generateCharacterProfile(params);
        return NextResponse.json(characterResult);

      case 'generate_worldbuilding':
        const worldResult = await contentGenerator.generateWorldBuilding(params);
        return NextResponse.json(worldResult);

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    logger.error('Content Generator API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Rate limiting for content service queries
    const clientIP = getClientIP(request);
    const rateLimitResult = aiLimiter.check(30, clientIP); // 30 requests per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    // Authentication required for content service access
    const authResult = await authenticateUser();
    if (!authResult.success) {
      return authResult.response!;
    }

    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    
    const serviceManager = ServiceManager.getInstance();
    await serviceManager.initialize();
    
    const contentGenerator = await serviceManager.getContentGenerator();
    
    if (!contentGenerator) {
      return NextResponse.json(
        { error: 'Content Generator service not available' },
        { status: 503 }
      );
    }

    switch (type) {
      case 'health':
        const healthResult = await contentGenerator.healthCheck();
        return NextResponse.json(healthResult);

      case 'templates':
        return NextResponse.json({
          success: true,
          data: {
            contentTypes: [
              'scene',
              'dialogue', 
              'description',
              'chapter',
              'character',
              'plot-outline'
            ],
            lengths: ['short', 'medium', 'long'],
            tones: [
              'dramatic',
              'comedic',
              'mysterious',
              'romantic',
              'dark',
              'lighthearted',
              'suspenseful',
              'melancholic'
            ],
            styles: [
              'literary',
              'commercial',
              'young-adult',
              'middle-grade',
              'literary-fiction',
              'genre-fiction'
            ]
          }
        });

      default:
        return NextResponse.json({
          service: 'content-generator',
          version: '1.0.0',
          endpoints: [
            'POST /api/services/content - Generate content',
            'GET /api/services/content?type=templates - Get available templates',
            'GET /api/services/content?type=health - Health check'
          ]
        });
    }
  } catch (error) {
    return handleRouteError(error, 'Content Generator');
  }
}