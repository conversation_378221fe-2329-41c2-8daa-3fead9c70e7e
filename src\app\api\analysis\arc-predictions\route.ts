import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import type { ArcPrediction, Character, Chapter } from '@/lib/types/character-development';
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { characterId, projectId, currentChapter = 1, timeframe = 'medium' } = await request.json();

    if (!characterId || !projectId) {
      return NextResponse.json(
        { error: 'Character ID and Project ID are required' },
        { status: 400 }
      );
    }

    // Verify user has access to this project
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', projectId)
      .single();

    if (!project || project.user_id !== user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Fetch character and chapter data
    const [characterResult, chaptersResult] = await Promise.all([
      supabase
        .from('characters')
        .select('*')
        .eq('id', characterId)
        .eq('project_id', projectId)
        .single(),
      supabase
        .from('chapters')
        .select('id, project_id, chapter_number, content, outline, status')
        .eq('project_id', projectId)
        .order('chapter_number', { ascending: true })
    ]);

    if (characterResult.error || !characterResult.data) {
      return NextResponse.json(
        { error: 'Character not found' },
        { status: 404 }
      );
    }

    if (chaptersResult.error) {
      throw new Error('Failed to fetch chapters');
    }

    // Generate arc predictions
    const prediction = await generateArcPredictions(
      characterResult.data,
      chaptersResult.data || [],
      currentChapter,
      timeframe
    );

    return NextResponse.json(prediction);
  } catch (error) {
    logger.error('Error generating arc predictions:', error);
    return NextResponse.json(
      { error: 'Failed to generate arc predictions' },
      { status: 500 }
    );
  }
}

async function generateArcPredictions(
  character: Character,
  chapters: Chapter[],
  currentChapter: number,
  timeframe: string
): Promise<ArcPrediction> {
  // Analyze character's development pattern so far
  const developmentAnalysis = analyzeCharacterDevelopment(character, chapters, currentChapter);
  
  // Determine current trajectory
  const currentTrajectory = determineTrajectory(developmentAnalysis);
  
  // Calculate completion likelihood
  const completionLikelihood = calculateCompletionLikelihood(
    character,
    developmentAnalysis,
    currentTrajectory
  );
  
  // Generate intervention suggestions
  const suggestedInterventions = generateInterventions(
    character,
    developmentAnalysis,
    currentChapter,
    timeframe
  );
  
  // Identify risk factors
  const riskFactors = identifyRiskFactors(character, developmentAnalysis, currentTrajectory);

  return {
    characterId: character.id,
    currentTrajectory,
    completionLikelihood,
    suggestedInterventions,
    riskFactors
  };
}

function analyzeCharacterDevelopment(character: Character, chapters: Chapter[], currentChapter: number) {
  const completedChapters = chapters.filter(c => 
    c.chapter_number < currentChapter && 
    (c.status === 'complete' || c.content)
  );

  const analysis = {
    totalChapters: completedChapters.length,
    characterPresence: 0,
    developmentMoments: 0,
    emotionalRange: { min: 50, max: 50, current: 50 },
    relationshipChanges: 0,
    skillProgression: 0,
    conflictResolution: 0,
    consistencyScore: 0,
    arcProgression: 0
  };

  if (completedChapters.length === 0) {
    return analysis;
  }

  // Analyze character presence and development
  let totalMentions = 0;
  let developmentKeywords = 0;
  const emotionalKeywords = { positive: 0, negative: 0, growth: 0 };
  let relationshipKeywords = 0;
  let skillKeywords = 0;
  let conflictKeywords = 0;

  completedChapters.forEach((chapter) => {
    const content = (chapter.content || '') + (chapter.outline || '');
    const characterName = character.name;

    // Count character mentions
    const mentions = countOccurrences(content, characterName);
    totalMentions += mentions;

    // Development indicators
    const devWords = ['grew', 'learned', 'realized', 'understood', 'changed', 'evolved', 'developed'];
    devWords.forEach(word => {
      if (content.toLowerCase().includes(word) && content.includes(characterName)) {
        developmentKeywords++;
      }
    });

    // Emotional progression
    const positiveWords = ['happy', 'joy', 'confident', 'proud', 'peaceful', 'hopeful'];
    const negativeWords = ['sad', 'angry', 'frustrated', 'defeated', 'desperate', 'afraid'];
    const growthWords = ['breakthrough', 'epiphany', 'revelation', 'acceptance', 'forgiveness'];

    positiveWords.forEach(word => {
      if (content.toLowerCase().includes(word) && content.includes(characterName)) {
        emotionalKeywords.positive++;
      }
    });

    negativeWords.forEach(word => {
      if (content.toLowerCase().includes(word) && content.includes(characterName)) {
        emotionalKeywords.negative++;
      }
    });

    growthWords.forEach(word => {
      if (content.toLowerCase().includes(word) && content.includes(characterName)) {
        emotionalKeywords.growth++;
      }
    });

    // Relationship changes
    const relationshipWords = ['befriended', 'allied', 'trust', 'betrayed', 'reconciled', 'connected'];
    relationshipWords.forEach(word => {
      if (content.toLowerCase().includes(word) && content.includes(characterName)) {
        relationshipKeywords++;
      }
    });

    // Skill progression
    const skillWords = ['mastered', 'learned', 'practiced', 'improved', 'discovered', 'acquired'];
    skillWords.forEach(word => {
      if (content.toLowerCase().includes(word) && content.includes(characterName)) {
        skillKeywords++;
      }
    });

    // Conflict resolution
    const conflictWords = ['resolved', 'overcome', 'conquered', 'defeated', 'succeeded', 'achieved'];
    conflictWords.forEach(word => {
      if (content.toLowerCase().includes(word) && content.includes(characterName)) {
        conflictKeywords++;
      }
    });
  });

  // Calculate metrics
  analysis.characterPresence = Math.min(100, (totalMentions / completedChapters.length / 5) * 100);
  analysis.developmentMoments = developmentKeywords;
  analysis.relationshipChanges = relationshipKeywords;
  analysis.skillProgression = skillKeywords;
  analysis.conflictResolution = conflictKeywords;

  // Emotional range calculation
  const emotionalActivity = emotionalKeywords.positive + emotionalKeywords.negative + emotionalKeywords.growth;
  analysis.emotionalRange = {
    min: Math.max(0, 50 - emotionalKeywords.negative * 5),
    max: Math.min(100, 50 + emotionalKeywords.positive * 5 + emotionalKeywords.growth * 10),
    current: 50 + (emotionalKeywords.positive - emotionalKeywords.negative) * 3 + emotionalKeywords.growth * 8
  };

  // Arc progression (based on character arc type and evidence)
  const arcType = character.character_arc?.type || 'unknown';
  analysis.arcProgression = calculateArcProgression(
    arcType,
    emotionalKeywords,
    developmentKeywords,
    completedChapters.length
  );

  // Consistency score
  analysis.consistencyScore = Math.min(100, 
    (analysis.characterPresence * 0.3) +
    (analysis.developmentMoments > 0 ? 30 : 0) +
    (emotionalActivity > 0 ? 25 : 0) +
    (analysis.arcProgression > 0 ? 15 : 0)
  );

  return analysis;
}

function determineTrajectory(analysis: {
  emotionalRange: { min: number; max: number; current: number };
  developmentMoments: number;
  arcProgression: number;
  consistencyScore: number;
}): 'positive' | 'negative' | 'stagnant' | 'volatile' {
  const { emotionalRange, developmentMoments, arcProgression, consistencyScore } = analysis;
  
  // Check for volatility (inconsistent development)
  if (consistencyScore < 40 || Math.abs(emotionalRange.max - emotionalRange.min) > 60) {
    return 'volatile';
  }
  
  // Check for positive trajectory
  if (developmentMoments >= 3 && emotionalRange.current > 60 && arcProgression > 50) {
    return 'positive';
  }
  
  // Check for negative trajectory
  if (emotionalRange.current < 40 || arcProgression < 20) {
    return 'negative';
  }
  
  // Check for stagnation
  if (developmentMoments < 2 && Math.abs(emotionalRange.current - 50) < 10) {
    return 'stagnant';
  }
  
  return 'positive'; // Default to positive if unclear
}

function calculateCompletionLikelihood(
  character: Character,
  analysis: {
    arcProgression: number;
    consistencyScore: number;
    developmentMoments: number;
    characterPresence: number;
  },
  trajectory: string
): number {
  // Suppress unused variable warning
  void character;
  let likelihood = 50; // Base likelihood

  // Factor in current progress
  likelihood += analysis.arcProgression * 0.3;
  
  // Factor in consistency
  likelihood += analysis.consistencyScore * 0.2;
  
  // Factor in development moments
  likelihood += Math.min(30, analysis.developmentMoments * 5);
  
  // Factor in trajectory
  switch (trajectory) {
    case 'positive':
      likelihood += 20;
      break;
    case 'negative':
      likelihood -= 10; // Negative can still complete, just differently
      break;
    case 'stagnant':
      likelihood -= 20;
      break;
    case 'volatile':
      likelihood -= 15;
      break;
  }
  
  // Factor in character presence
  if (analysis.characterPresence < 30) {
    likelihood -= 25;
  }
  
  return Math.max(0, Math.min(100, likelihood));
}

function generateInterventions(
  character: Character,
  analysis: {
    characterPresence: number;
    emotionalRange: { min: number; max: number; current: number };
    skillProgression: number;
    relationshipChanges: number;
    arcProgression: number;
  },
  currentChapter: number,
  timeframe: string
) {
  // Suppress unused variable warning
  void character;
  const interventions = [];
  const chaptersAhead = timeframe === 'short' ? 5 : timeframe === 'medium' ? 15 : 25;
  
  // Low character presence intervention
  if (analysis.characterPresence < 40) {
    interventions.push({
      dimension: 'Character Presence',
      chapters: Array.from({ length: 3 }, (_, i) => currentChapter + i + 1),
      action: 'Increase character involvement in key scenes. Add more dialogue, internal thoughts, or actions that drive the plot forward.',
      impact: 'high' as const
    });
  }
  
  // Emotional development intervention
  if (analysis.emotionalRange.current === analysis.emotionalRange.min) {
    interventions.push({
      dimension: 'Emotional Growth',
      chapters: Array.from({ length: 2 }, (_, i) => currentChapter + Math.floor(chaptersAhead / 3) + i),
      action: 'Create emotional challenges or revelations that push the character out of their comfort zone.',
      impact: 'medium' as const
    });
  }
  
  // Skill development intervention
  if (analysis.skillProgression < 2) {
    interventions.push({
      dimension: 'Skill Progression',
      chapters: [currentChapter + Math.floor(chaptersAhead / 2)],
      action: 'Introduce training sequences, mentor figures, or challenges that require new abilities.',
      impact: 'medium' as const
    });
  }
  
  // Relationship development intervention
  if (analysis.relationshipChanges < 1) {
    interventions.push({
      dimension: 'Relationships',
      chapters: Array.from({ length: 2 }, (_, i) => currentChapter + i + 2),
      action: 'Develop meaningful interactions with other characters. Consider adding conflicts or bonding moments.',
      impact: 'high' as const
    });
  }
  
  // Arc progression intervention
  if (analysis.arcProgression < 30) {
    interventions.push({
      dimension: 'Arc Progression',
      chapters: [currentChapter + Math.floor(chaptersAhead * 0.7)],
      action: 'Add a major turning point or revelation that advances the character toward their arc goals.',
      impact: 'high' as const
    });
  }
  
  return interventions;
}

function identifyRiskFactors(
  character: Character,
  analysis: {
    characterPresence: number;
    developmentMoments: number;
    consistencyScore: number;
    emotionalRange: { current: number };
    relationshipChanges: number;
  },
  trajectory: string
): string[] {
  // Suppress unused variable warning
  void character;
  const risks = [];
  
  if (analysis.characterPresence < 30) {
    risks.push('Character has low presence in recent chapters, risking reader disconnection');
  }
  
  if (analysis.developmentMoments < 2) {
    risks.push('Insufficient character development moments may lead to static arc');
  }
  
  if (analysis.consistencyScore < 50) {
    risks.push('Inconsistent character behavior may confuse readers');
  }
  
  if (trajectory === 'stagnant') {
    risks.push('Character showing little growth - may become uninteresting');
  }
  
  if (trajectory === 'volatile') {
    risks.push('Erratic character development may seem unrealistic');
  }
  
  if (analysis.emotionalRange.current < 30) {
    risks.push('Character stuck in negative emotional state for too long');
  }
  
  if (analysis.relationshipChanges === 0) {
    risks.push('Lack of relationship development may isolate character');
  }
  
  return risks;
}

function calculateArcProgression(
  arcType: string,
  emotionalKeywords: { positive: number; negative: number; growth: number },
  developmentMoments: number,
  chaptersCompleted: number
): number {
  let progression = 0;
  
  // Base progression from development moments
  progression += Math.min(50, developmentMoments * 10);
  
  // Arc-specific progression
  switch (arcType) {
    case 'positive_change':
    case 'redemption':
      progression += emotionalKeywords.growth * 15 + emotionalKeywords.positive * 5;
      break;
    case 'negative_change':
    case 'corruption':
      progression += emotionalKeywords.negative * 10;
      break;
    case 'flat_arc':
      progression += emotionalKeywords.growth * 10; // Less emphasis on change
      break;
  }
  
  // Factor in time (assuming arcs should progress over time)
  const timeProgression = Math.min(30, chaptersCompleted * 2);
  progression += timeProgression;
  
  return Math.min(100, progression);
}

// Helper functions
function countOccurrences(text: string, searchTerm: string): number {
  const regex = new RegExp(searchTerm, 'gi');
  const matches = text.match(regex);
  return matches ? matches.length : 0;
}