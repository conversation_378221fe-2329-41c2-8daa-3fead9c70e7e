import { logger } from '@/lib/services/logger'
/**
 * AI Streaming Demo Component
 * Comprehensive demonstration of all streaming AI features
 */

'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { AI_MODELS } from '@/lib/config/ai-settings'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  useStreamingContent, 
  useStreamingChat, 
  useStreamingStructuredContent 
} from '@/hooks/use-streaming-ai'
import { 
  Sparkles, 
  MessageSquare, 
  FileText, 
  Users, 
  Globe,
  Play,
  Square,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function AIStreamingDemo() {
  const [activeDemo, setActiveDemo] = useState('content')

  // Content generation demo
  const {
    content: generatedContent,
    isLoading: isGenerating,
    isStreaming: isContentStreaming,
    error: contentError,
    progress: contentProgress,
    quality: contentQuality,
    generateContent,
    cancelGeneration,
    reset: resetContent
  } = useStreamingContent({
    onProgress: (progress) => {
      logger.info('Content progress:', progress)
    },
    onQualityUpdate: (quality) => {
      logger.info('Content quality update:', quality)
    }
  })

  // Chat demo
  const {
    messages,
    input: chatInput,
    handleInputChange,
    handleSubmit: handleChatSubmit,
    isLoading: isChatLoading,
    clearChat
  } = useStreamingChat({
    systemPrompt: 'You are a creative writing assistant. Help users with their writing projects.',
    onComplete: (message) => {
      logger.info('Chat completed:', message)
    }
  })

  // Structured content demo
  const {
    content: structuredContent,
    structuredData,
    isLoading: isStructuredLoading,
    isStreaming: isStructuredStreaming,
    error: structuredError,
    progress: structuredProgress,
    generateStructuredContent,
    reset: resetStructured
  } = useStreamingStructuredContent('/api/ai/structured-content', {
    onProgress: (progress) => {
      logger.info('Structured progress:', progress)
    }
  })

  const [contentPrompt, setContentPrompt] = useState('Write a mysterious scene in a haunted library at midnight.')
  const [selectedModel, setSelectedModel] = useState<string>(AI_MODELS.XAI_PRIMARY)
  const [structuredParams, setStructuredParams] = useState({
    contentType: 'scene_outline',
    prompt: 'Create a scene outline for a tense confrontation between the protagonist and antagonist in a medieval castle.',
    parameters: {
      chapterTitle: 'The Final Confrontation',
      tone: 'dramatic',
      style: 'epic fantasy'
    }
  })

  const handleGenerateContent = () => {
    const systemPrompt = selectedModel === AI_MODELS.XAI_PRIMARY
      ? 'You are Grok, an expert creative writer with wit and personality. Create vivid, engaging content with your unique style.'
      : 'You are an expert creative writer. Create vivid, engaging content.'

    generateContent(
      contentPrompt,
      systemPrompt,
      1000,
      selectedModel
    )
  }

  const handleGenerateStructured = () => {
    generateStructuredContent(structuredParams, 1500)
  }

  const getStatusIcon = (isLoading: boolean, isStreaming: boolean, error: any) => {
    if (error) return <AlertCircle className="h-4 w-4 text-red-500" />
    if (isStreaming) return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
    if (isLoading) return <RefreshCw className="h-4 w-4 animate-spin text-yellow-500" />
    return <CheckCircle className="h-4 w-4 text-green-500" />
  }

  const getQualityBadge = (quality?: number) => {
    if (!quality) return null
    
    const getColor = () => {
      if (quality >= 80) return 'bg-green-500'
      if (quality >= 60) return 'bg-yellow-500'
      return 'bg-red-500'
    }

    const getLabel = () => {
      if (quality >= 80) return 'Excellent'
      if (quality >= 60) return 'Good'
      return 'Needs Work'
    }

    return (
      <Badge className={cn('text-white', getColor())}>
        {getLabel()} ({Math.round(quality)}%)
      </Badge>
    )
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Sparkles className="h-8 w-8 text-purple-500" />
          AI Streaming Demo
        </h1>
        <p className="text-muted-foreground">
          Experience real-time AI content generation with the Vercel AI SDK
        </p>
      </div>

      <Tabs value={activeDemo} onValueChange={setActiveDemo} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="content" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Content Generation
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Chat Assistant
          </TabsTrigger>
          <TabsTrigger value="structured" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Structured Content
          </TabsTrigger>
        </TabsList>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Streaming Content Generation</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(isGenerating, isContentStreaming, contentError)}
                  {getQualityBadge(contentQuality)}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="model-selection">AI Model</Label>
                <div className="flex gap-2 flex-wrap">
                  {([
                    { id: AI_MODELS.XAI_PRIMARY, name: 'Grok 3', desc: 'Creative & Witty', color: 'bg-purple-500' },
                    { id: AI_MODELS.ALTERNATIVE_PRIMARY, name: 'GPT-4 Turbo', desc: 'Structured & Precise', color: 'bg-blue-500' },
                    { id: AI_MODELS.FAST, name: 'GPT-4o Mini', desc: 'Fast & Efficient', color: 'bg-green-500' }
                  ] as const).map((model) => (
                    <Button
                      key={model.id}
                      variant={selectedModel === model.id ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedModel(model.id as any)}
                      className={cn(
                        'flex flex-col h-auto p-3',
                        selectedModel === model.id && `${model.color} text-white`
                      )}
                      disabled={isGenerating}
                    >
                      <span className="text-xs font-medium">{model.name}</span>
                      <span className="text-xs opacity-70">{model.desc}</span>
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="content-prompt">Content Prompt</Label>
                <Textarea
                  id="content-prompt"
                  value={contentPrompt}
                  onChange={(e) => setContentPrompt(e.target.value)}
                  placeholder="Describe what you want me to write..."
                  className="min-h-[100px]"
                  disabled={isGenerating}
                />
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onClick={handleGenerateContent}
                  disabled={!contentPrompt.trim() || isGenerating}
                  className="flex items-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  Generate Content
                </Button>

                {isGenerating && (
                  <Button
                    variant="outline"
                    onClick={cancelGeneration}
                    className="flex items-center gap-2"
                  >
                    <Square className="h-4 w-4" />
                    Stop
                  </Button>
                )}

                {generatedContent && !isGenerating && (
                  <Button
                    variant="outline"
                    onClick={resetContent}
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Reset
                  </Button>
                )}
              </div>

              {isContentStreaming && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(contentProgress.percentComplete)}%</span>
                  </div>
                  <Progress value={contentProgress.percentComplete} />
                  <div className="text-xs text-muted-foreground">
                    Tokens: {contentProgress.tokens} / {contentProgress.estimatedTokens}
                  </div>
                </div>
              )}

              {contentError && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{contentError}</p>
                </div>
              )}

              {generatedContent && (
                <div className="space-y-2">
                  <Label>Generated Content</Label>
                  <div className="p-4 bg-gray-50 rounded-md border max-h-96 overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm">
                      {generatedContent}
                    </pre>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chat" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Streaming Chat Assistant</span>
                {getStatusIcon(isChatLoading, isChatLoading, null)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4 max-h-96 overflow-y-auto p-4 bg-gray-50 rounded-md border">
                {messages.length === 0 ? (
                  <div className="text-center text-muted-foreground">
                    <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>Start a conversation with your AI assistant</p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className={cn(
                        'flex gap-3 p-3 rounded-lg',
                        message.role === 'user'
                          ? 'bg-blue-100 ml-8'
                          : 'bg-white mr-8 shadow-sm'
                      )}
                    >
                      <div className="flex-1">
                        <div className="text-sm font-medium mb-1">
                          {message.role === 'user' ? 'You' : 'AI Assistant'}
                        </div>
                        <div className="text-sm whitespace-pre-wrap">
                          {message.content}
                        </div>
                      </div>
                    </div>
                  ))
                )}
                
                {isChatLoading && (
                  <div className="flex gap-3 p-3 rounded-lg bg-white mr-8 shadow-sm">
                    <div className="flex-1">
                      <div className="text-sm font-medium mb-1">AI Assistant</div>
                      <div className="flex items-center gap-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span className="text-sm text-muted-foreground">Thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <form onSubmit={handleChatSubmit} className="flex gap-2">
                <Textarea
                  value={chatInput}
                  onChange={handleInputChange}
                  placeholder="Ask me anything about writing..."
                  className="flex-1 min-h-[60px] resize-none"
                  disabled={isChatLoading}
                />
                <div className="flex flex-col gap-2">
                  <Button
                    type="submit"
                    disabled={!chatInput.trim() || isChatLoading}
                  >
                    Send
                  </Button>
                  {messages.length > 0 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={clearChat}
                      disabled={isChatLoading}
                    >
                      Clear
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="structured" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Streaming Structured Content</span>
                {getStatusIcon(isStructuredLoading, isStructuredStreaming, structuredError)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="content-type">Content Type</Label>
                  <select
                    id="content-type"
                    value={structuredParams.contentType}
                    onChange={(e) => setStructuredParams(prev => ({
                      ...prev,
                      contentType: e.target.value as any
                    }))}
                    className="w-full p-2 border rounded-md"
                    disabled={isStructuredLoading}
                  >
                    <option value="scene_outline">Scene Outline</option>
                    <option value="dialogue">Dialogue</option>
                    <option value="character_profile">Character Profile</option>
                    <option value="world_building">World Building</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="chapter-title">Chapter Title</Label>
                  <Input
                    id="chapter-title"
                    value={structuredParams.parameters.chapterTitle}
                    onChange={(e) => setStructuredParams(prev => ({
                      ...prev,
                      parameters: {
                        ...prev.parameters,
                        chapterTitle: e.target.value
                      }
                    }))}
                    disabled={isStructuredLoading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="structured-prompt">Prompt</Label>
                <Textarea
                  id="structured-prompt"
                  value={structuredParams.prompt}
                  onChange={(e) => setStructuredParams(prev => ({
                    ...prev,
                    prompt: e.target.value
                  }))}
                  placeholder="Describe the structured content you want..."
                  className="min-h-[100px]"
                  disabled={isStructuredLoading}
                />
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onClick={handleGenerateStructured}
                  disabled={!structuredParams.prompt.trim() || isStructuredLoading}
                  className="flex items-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  Generate Structured Content
                </Button>

                {structuredData && !isStructuredLoading && (
                  <Button
                    variant="outline"
                    onClick={resetStructured}
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Reset
                  </Button>
                )}
              </div>

              {isStructuredStreaming && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(structuredProgress.percentComplete)}%</span>
                  </div>
                  <Progress value={structuredProgress.percentComplete} />
                </div>
              )}

              {structuredError && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{structuredError}</p>
                </div>
              )}

              {structuredData && (
                <div className="space-y-2">
                  <Label>Generated Structured Data</Label>
                  <div className="p-4 bg-gray-50 rounded-md border max-h-96 overflow-y-auto">
                    <pre className="text-sm">
                      {JSON.stringify(structuredData, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
