import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { getRelationshipAnalyzer } from '@/lib/relationships/relationship-instances';
import { authenticateUserForProject } from '@/lib/api/auth-helpers';
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    const { projectId, chapterRange } = await request.json();
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    // Check authentication and project access
    const { user, error: authError } = await authenticateUserForProject(projectId);
    if (authError) return authError;

    const analyzer = getRelationshipAnalyzer(projectId);
    
    // Generate relationship graph
    const graph = await analyzer.generateRelationshipGraph(chapterRange);
    
    return NextResponse.json({
      success: true,
      ...graph
    });

  } catch (error) {
    logger.error('Error generating relationship graph:', error);
    return NextResponse.json(
      { error: 'Failed to generate relationship graph' },
      { status: 500 }
    );
  }
}

