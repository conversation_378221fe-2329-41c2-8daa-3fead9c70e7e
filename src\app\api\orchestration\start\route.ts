import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator';
import { orchestratorInstances } from '@/lib/agents/orchestrator-instances';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import type { ProjectSettings } from '@/lib/types/project-settings';
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      projectId, 
      projectSelections, 
      storyPrompt, 
      targetWordCount, 
      targetChapters 
    } = body;

    if (!projectId || !projectSelections || !storyPrompt) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Verify user owns the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    // Check if orchestration is already running for this project
    if (orchestratorInstances.has(projectId)) {
      return NextResponse.json(
        { error: 'Orchestration already running for this project' },
        { status: 409 }
      );
    }

    // Create new orchestrator instance
    const orchestrator = new AdvancedAgentOrchestrator(3); // 3 concurrent tasks
    orchestratorInstances.set(projectId, orchestrator);

    // Set up event listeners for progress tracking
    orchestrator.on('orchestration:started', (data) => {
      logger.info(`=� Orchestration started for project ${projectId}:`, data);
    });

    orchestrator.on('task:started', (data) => {
      logger.info(`� Task started: ${data.taskId} (${data.type})`);
    });

    orchestrator.on('task:completed', (data) => {
      logger.info(` Task completed: ${data.taskId}`);
    });

    orchestrator.on('task:failed', (data) => {
      logger.info(`L Task failed: ${data.taskId} - ${data.error}`);
    });

    orchestrator.on('orchestration:completed', async (data) => {
      logger.info(`<� Orchestration completed for project ${projectId}:`, data);
      
      // Clean up the instance after completion
      setTimeout(() => {
        orchestratorInstances.delete(projectId);
      }, 30000); // Keep for 30 seconds for final status checks
    });

    orchestrator.on('orchestration:cancelled', () => {
      logger.info(`=� Orchestration cancelled for project ${projectId}`);
      orchestratorInstances.delete(projectId);
    });

    // Start orchestration asynchronously
    const orchestrationPromise = orchestrator.orchestrateProject(
      projectId,
      projectSelections as ProjectSettings,
      storyPrompt,
      targetWordCount || 80000,
      targetChapters || 20
    );

    // Don't await the orchestration - let it run in background
    orchestrationPromise.then(async (result) => {
      if (result.success && result.data) {
        // Store the completed book context in the database
        try {
          const supabase = await createServerSupabaseClient();
          const { error: updateError } = await supabase
            .from('projects')
            .update({
              book_context: result.data,
              status: 'outlined',
              updated_at: new Date().toISOString()
            })
            .eq('id', projectId);

          if (updateError) {
            logger.error('Failed to update project with book context:', updateError);
          } else {
            logger.info(`=� Book context saved for project ${projectId}`);
          }
        } catch (error) {
          logger.error('Error saving book context:', error);
        }
      }
    }).catch((error) => {
      logger.error(`Orchestration failed for project ${projectId}:`, error);
    });

    return NextResponse.json({
      success: true,
      message: 'Orchestration started',
      projectId,
      estimatedDuration: '10-15 minutes',
      progressEndpoint: `/api/orchestration/progress?projectId=${projectId}`
    });

  } catch (error) {
    logger.error('Error starting orchestration:', error);
    return NextResponse.json(
      { error: 'Failed to start orchestration' },
      { status: 500 }
    );
  }
}