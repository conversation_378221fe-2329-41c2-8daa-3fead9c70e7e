import { logger } from '../services/logger'

interface ZeruhVerificationResponse {
  valid: boolean
  email_address: string
  is_disposable: boolean
  is_role_based: boolean
  is_free: boolean
  is_syntax_valid: boolean
  is_domain_valid: boolean
  is_smtp_valid: boolean
  is_catchall: boolean
  is_mailbox_full: boolean
  is_no_reply: boolean
  quality_score: number
  time_taken: string
  status: string
  reason?: string
  did_you_mean?: string
}

interface EmailVerificationResult {
  isValid: boolean
  email: string
  reason?: string
  suggestion?: string
  details?: {
    isDisposable: boolean
    isRoleBased: boolean
    isFree: boolean
    qualityScore: number
  }
}

/**
 * Verify email address using Zeruh API
 * Documentation: https://zeruh.com/api-docs
 */
export async function verifyEmailWithZeruh(email: string): Promise<EmailVerificationResult> {
  const apiKey = process.env.ZERUH_API_KEY
  
  if (!apiKey) {
    logger.error('ZERUH_API_KEY not configured')
    throw new Error('Email verification service not configured')
  }

  // Basic format validation first
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    return {
      isValid: false,
      email,
      reason: 'Invalid email format'
    }
  }

  try {
    const endpoint = `https://api.zeruh.com/v1/verify?api_key=${apiKey}&email_address=${encodeURIComponent(email)}`
    
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      logger.error('Zeruh API error', {
        status: response.status,
        statusText: response.statusText
      })
      
      // Fallback to basic validation if API fails
      return {
        isValid: true, // Allow email if verification service is down
        email,
        reason: 'Email verification service temporarily unavailable'
      }
    }

    const data: ZeruhVerificationResponse = await response.json()

    // Log verification result
    logger.info('Email verification completed', {
      email,
      valid: data.valid,
      qualityScore: data.quality_score,
      timeTaken: data.time_taken
    })

    // Determine if email should be accepted
    const shouldAccept = data.valid && 
                        !data.is_disposable && 
                        !data.is_catchall &&
                        !data.is_mailbox_full &&
                        data.quality_score >= 0.5

    return {
      isValid: shouldAccept,
      email: data.email_address,
      reason: !shouldAccept ? determineRejectionReason(data) : undefined,
      suggestion: data.did_you_mean,
      details: {
        isDisposable: data.is_disposable,
        isRoleBased: data.is_role_based,
        isFree: data.is_free,
        qualityScore: data.quality_score
      }
    }

  } catch (error) {
    logger.error('Failed to verify email with Zeruh', error)
    
    // In case of network error, allow the email but log the issue
    return {
      isValid: true,
      email,
      reason: 'Email verification service error - allowing email'
    }
  }
}

/**
 * Determine the reason for email rejection
 */
function determineRejectionReason(data: ZeruhVerificationResponse): string {
  if (!data.valid) {
    return data.reason || 'Invalid email address'
  }
  if (data.is_disposable) {
    return 'Disposable email addresses are not allowed'
  }
  if (data.is_catchall) {
    return 'Catch-all email addresses are not allowed'
  }
  if (data.is_mailbox_full) {
    return 'Email mailbox is full'
  }
  if (data.quality_score < 0.5) {
    return 'Email address quality score too low'
  }
  return 'Email address verification failed'
}

/**
 * Batch verify multiple email addresses
 */
export async function batchVerifyEmails(emails: string[]): Promise<EmailVerificationResult[]> {
  const results: EmailVerificationResult[] = []
  
  // Process emails in batches to avoid rate limiting
  const batchSize = 10
  for (let i = 0; i < emails.length; i += batchSize) {
    const batch = emails.slice(i, i + batchSize)
    const batchResults = await Promise.all(
      batch.map(email => verifyEmailWithZeruh(email))
    )
    results.push(...batchResults)
    
    // Add small delay between batches
    if (i + batchSize < emails.length) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }
  
  return results
}

/**
 * Verify email and get suggestion if invalid
 */
export async function verifyAndSuggest(email: string): Promise<{
  isValid: boolean
  suggestion?: string
  message?: string
}> {
  const result = await verifyEmailWithZeruh(email)
  
  if (result.isValid) {
    return { isValid: true }
  }
  
  let message = result.reason || 'Invalid email address'
  
  if (result.suggestion) {
    message += `. Did you mean ${result.suggestion}?`
  }
  
  return {
    isValid: false,
    suggestion: result.suggestion,
    message
  }
}

/**
 * Check if email domain is from a known provider
 */
export function isKnownEmailProvider(email: string): boolean {
  const knownProviders = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'aol.com',
    'icloud.com',
    'protonmail.com',
    'mail.com',
    'zoho.com',
    'yandex.com'
  ]
  
  const domain = email.split('@')[1]?.toLowerCase()
  return knownProviders.includes(domain)
}

/**
 * Sanitize email for safe storage
 */
export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim()
}