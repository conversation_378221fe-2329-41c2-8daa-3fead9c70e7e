import { useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/contexts/auth-context'
import { logger } from '@/lib/services/logger'

interface TrackingOptions {
  immediate?: boolean
  debounce?: number
}

export function useAchievementTracker() {
  const { user } = useAuth()
  const supabase = createClient()

  const trackProgress = async (
    achievementCode: string,
    progressKey: string,
    increment: number = 1
  ) => {
    if (!user) return

    try {
      const { error } = await supabase.rpc('track_achievement_progress', {
        p_user_id: user.id,
        p_achievement_code: achievementCode,
        p_progress_key: progressKey,
        p_increment: increment
      })

      if (error) {
        logger.error('Failed to track achievement progress:', error)
      }
    } catch (error) {
      logger.error('Achievement tracking error:', error)
    }
  }

  // Track export achievement
  const trackExport = async (format: string) => {
    await trackProgress('first_export', 'exports', 1)
    await trackProgress('export_master', format, 1)
  }

  // Track writing streak
  const trackDailyWriting = async () => {
    const today = new Date().toISOString().split('T')[0]
    await trackProgress('week_streak', today, 1)
    await trackProgress('month_streak', today, 1)
  }

  // Track AI agent usage
  const trackAgentUsage = async (agentName: string) => {
    await trackProgress('ai_explorer', agentName, 1)
  }

  return {
    trackProgress,
    trackExport,
    trackDailyWriting,
    trackAgentUsage
  }
}

// Hook to automatically track word count achievements
export function useWordCountTracker(
  wordCount: number,
  options: TrackingOptions = {}
) {
  const { user } = useAuth()
  const supabase = createClient()

  useEffect(() => {
    if (!user || !wordCount) return

    const checkWordCountAchievements = async () => {
      try {
        // This will trigger the database function to check achievements
        await supabase.rpc('check_and_unlock_achievements', {
          p_user_id: user.id
        })
      } catch (error) {
        logger.error('Failed to check word count achievements:', error)
      }
    }

    if (options.immediate) {
      checkWordCountAchievements()
    } else {
      const timeout = setTimeout(
        checkWordCountAchievements,
        options.debounce || 5000
      )
      return () => clearTimeout(timeout)
    }
  }, [wordCount, user, options.immediate, options.debounce])
}