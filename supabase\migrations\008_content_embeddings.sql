-- Content Embeddings for Semantic Search
-- This table stores vector embeddings for content pieces to enable semantic search

-- Enable vector extension for embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Content Embeddings Table
CREATE TABLE content_embeddings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  content_type VARCHAR(50) NOT NULL, -- 'chapter', 'character', 'story_bible', 'reference'
  content_id UUID NOT NULL, -- ID of the referenced content
  text_content TEXT NOT NULL, -- The actual text that was embedded
  embedding vector(1536), -- OpenAI embedding dimension
  metadata JSONB, -- Additional context about the content
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_content_embeddings_project_id ON content_embeddings(project_id);
CREATE INDEX idx_content_embeddings_content_type ON content_embeddings(content_type);
CREATE INDEX idx_content_embeddings_content_id ON content_embeddings(content_id);

-- Vector similarity search index
CREATE INDEX idx_content_embeddings_embedding ON content_embeddings 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Enable RLS
ALTER TABLE content_embeddings ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own content embeddings" ON content_embeddings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = content_embeddings.project_id 
      AND p.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own content embeddings" ON content_embeddings
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = content_embeddings.project_id 
      AND p.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own content embeddings" ON content_embeddings
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = content_embeddings.project_id 
      AND p.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete own content embeddings" ON content_embeddings
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = content_embeddings.project_id 
      AND p.user_id = auth.uid()
    )
  );

-- Function to search similar content
CREATE OR REPLACE FUNCTION search_similar_content(
  query_embedding vector(1536),
  project_uuid UUID,
  content_type_filter VARCHAR(50) DEFAULT NULL,
  similarity_threshold FLOAT DEFAULT 0.7,
  match_count INT DEFAULT 10
)
RETURNS TABLE (
  id UUID,
  content_type VARCHAR(50),
  content_id UUID,
  text_content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ce.id,
    ce.content_type,
    ce.content_id,
    ce.text_content,
    ce.metadata,
    1 - (ce.embedding <=> query_embedding) as similarity
  FROM content_embeddings ce
  WHERE 
    ce.project_id = project_uuid
    AND (content_type_filter IS NULL OR ce.content_type = content_type_filter)
    AND 1 - (ce.embedding <=> query_embedding) > similarity_threshold
  ORDER BY ce.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Update trigger for updated_at
CREATE TRIGGER update_content_embeddings_updated_at
  BEFORE UPDATE ON content_embeddings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();