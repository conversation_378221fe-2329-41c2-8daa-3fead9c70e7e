import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'

// Update series schema
const updateSeriesSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().max(500).nullable().optional(),
  genre: z.string().max(50).nullable().optional(),
  planned_book_count: z.number().int().positive().nullable().optional(),
  publication_status: z.enum(['planning', 'active', 'completed', 'hiatus']).optional(),
})

// GET - Fetch a specific series
export async function GET(
  _request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch series with books
    const { data: series, error } = await supabase
      .from('series')
      .select(`
        *,
        series_books (
          book_number,
          book_role,
          projects (
            id,
            title,
            description,
            status,
            current_word_count,
            target_word_count,
            primary_genre
          )
        )
      `)
      .eq('id', seriesId)
      .eq('user_id', user.id)
      .single()

    if (error || !series) {
      return NextResponse.json({ error: 'Series not found' }, { status: 404 })
    }

    return NextResponse.json({ series })
  } catch (error) {
    logger.error('Error in series GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PATCH - Update a series
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = updateSeriesSchema.parse(body)

    // Verify ownership
    const { data: existingSeries } = await supabase
      .from('series')
      .select('id')
      .eq('id', seriesId)
      .eq('user_id', user.id)
      .single()

    if (!existingSeries) {
      return NextResponse.json({ error: 'Series not found' }, { status: 404 })
    }

    // Update series
    const { data: series, error } = await supabase
      .from('series')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', seriesId)
      .select()
      .single()

    if (error) {
      logger.error('Error updating series:', error)
      return NextResponse.json({ error: 'Failed to update series' }, { status: 500 })
    }

    return NextResponse.json({ series })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input', details: error.errors }, { status: 400 })
    }
    logger.error('Error in series PATCH:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE - Delete a series
export async function DELETE(
  _request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify ownership and check if series has books
    const { data: series } = await supabase
      .from('series')
      .select(`
        id,
        series_books (
          project_id
        )
      `)
      .eq('id', seriesId)
      .eq('user_id', user.id)
      .single()

    if (!series) {
      return NextResponse.json({ error: 'Series not found' }, { status: 404 })
    }

    // Don't allow deletion if series has books
    if (series.series_books && series.series_books.length > 0) {
      return NextResponse.json({ 
        error: 'Cannot delete series with books. Remove all books first.' 
      }, { status: 400 })
    }

    // Delete series
    const { error } = await supabase
      .from('series')
      .delete()
      .eq('id', seriesId)

    if (error) {
      logger.error('Error deleting series:', error)
      return NextResponse.json({ error: 'Failed to delete series' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    logger.error('Error in series DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}