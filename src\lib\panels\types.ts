import { ComponentType } from 'react'
import { LucideIcon } from 'lucide-react'

// Generic types for flexible data structures
export type PanelMetadata = Record<string, unknown>
export type PanelSettings = Record<string, unknown>
export type ActionData = unknown

export interface PanelPlugin {
  id: string
  name: string
  description: string
  icon: LucideIcon
  component: ComponentType<PanelProps>
  category: 'writing' | 'analysis' | 'series' | 'tools' | 'ai'
  defaultPosition: 'left' | 'right' | 'bottom' | 'floating'
  defaultWidth?: number
  defaultHeight?: number
  minWidth?: number
  maxWidth?: number
  minHeight?: number
  maxHeight?: number
  permissions?: string[]
  dependencies?: string[]
  shortcuts?: PanelShortcut[]
  canFloat?: boolean
  canPin?: boolean
  showInMenu?: boolean
}

export interface PanelProps {
  projectId: string
  userId?: string
  chapterId?: string
  content?: string
  selectedText?: string
  seriesId?: string
  metadata?: PanelMetadata
  onClose: () => void
  onAction: (action: string, data?: ActionData) => void
  onSettingsChange?: (settings: PanelSettings) => void
}

export interface PanelShortcut {
  key: string
  modifiers?: ('ctrl' | 'alt' | 'shift' | 'meta')[]
  description: string
  action: string
}

export interface PanelState {
  id: string
  visible: boolean
  pinned: boolean
  position: 'left' | 'right' | 'bottom' | 'floating'
  width?: number
  height?: number
  x?: number
  y?: number
  settings?: PanelSettings
}

export interface PanelLayout {
  id: string
  name: string
  description: string
  panels: PanelState[]
  isDefault?: boolean
  createdAt?: string
  updatedAt?: string
}

export interface PanelContext {
  plugins: Map<string, PanelPlugin>
  activeLayout: PanelLayout
  panelStates: Map<string, PanelState>
  registerPlugin: (plugin: PanelPlugin) => void
  unregisterPlugin: (id: string) => void
  togglePanel: (id: string) => void
  updatePanelState: (id: string, state: Partial<PanelState>) => void
  savePanelLayout: (name: string) => void
  loadPanelLayout: (id: string) => void
  resetToDefaultLayout: () => void
}

// Panel lifecycle hooks
export interface PanelLifecycle {
  onMount?: () => void | Promise<void>
  onUnmount?: () => void | Promise<void>
  onActivate?: () => void | Promise<void>
  onDeactivate?: () => void | Promise<void>
  onResize?: (width: number, height: number) => void
  onSettingsChange?: (settings: PanelSettings) => void
  onContentChange?: (content: string) => void
}

// Panel communication events
export interface PanelEvent {
  source: string
  target?: string
  type: string
  data?: ActionData
  timestamp: number
}

export type PanelEventHandler = (event: PanelEvent) => void

// Default panel layouts
export const DEFAULT_LAYOUTS: Record<string, PanelLayout> = {
  writing: {
    id: 'writing',
    name: 'Writing Mode',
    description: 'Optimized for content creation',
    panels: [
      { id: 'chapters', visible: true, pinned: false, position: 'left' },
      { id: 'knowledge', visible: true, pinned: false, position: 'right' },
      { id: 'ai-chat', visible: false, pinned: false, position: 'right' }
    ],
    isDefault: true
  },
  editing: {
    id: 'editing',
    name: 'Editing Mode',
    description: 'Focus on refinement and analysis',
    panels: [
      { id: 'chapters', visible: true, pinned: false, position: 'left' },
      { id: 'voice-analysis', visible: true, pinned: false, position: 'right' },
      { id: 'consistency', visible: true, pinned: false, position: 'right' }
    ]
  },
  planning: {
    id: 'planning',
    name: 'Planning Mode',
    description: 'Story structure and organization',
    panels: [
      { id: 'story-bible', visible: true, pinned: false, position: 'left' },
      { id: 'character-arcs', visible: true, pinned: false, position: 'right' },
      { id: 'plot-structure', visible: true, pinned: false, position: 'bottom' }
    ]
  }
}