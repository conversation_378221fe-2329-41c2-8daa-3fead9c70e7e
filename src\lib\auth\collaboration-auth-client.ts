import { createClient } from '@/lib/supabase/client'
import { logger } from '@/lib/services/logger'

export interface CollaborationPermissions {
  canView: boolean
  canEdit: boolean
  canManageTeam: boolean
  canExport: boolean
  canDelete: boolean
  role: 'owner' | 'editor' | 'viewer' | null
}

/**
 * Check if a user has access to a project and what permissions they have (client-side version)
 */
export async function checkProjectAccess(
  userId: string,
  projectId: string
): Promise<CollaborationPermissions> {
  const supabase = createClient()
  
  // Default permissions (no access)
  const defaultPermissions: CollaborationPermissions = {
    canView: false,
    canEdit: false,
    canManageTeam: false,
    canExport: false,
    canDelete: false,
    role: null
  }
  
  try {
    // Check if user owns the project
    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', projectId)
      .single()
    
    if (!project) {
      return defaultPermissions
    }
    
    if (project.user_id === userId) {
      // Owner has all permissions
      return {
        canView: true,
        canEdit: true,
        canManageTeam: true,
        canExport: true,
        canDelete: true,
        role: 'owner'
      }
    }
    
    // Check if user is a collaborator
    const { data: collaborator } = await supabase
      .from('project_collaborators')
      .select('role, permissions, status')
      .eq('project_id', projectId)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single()
    
    if (!collaborator) {
      return defaultPermissions
    }
    
    // Return permissions based on role
    return {
      canView: true, // All collaborators can view
      canEdit: collaborator.permissions?.can_write || false,
      canManageTeam: collaborator.permissions?.can_manage_team || false,
      canExport: collaborator.permissions?.can_export || false,
      canDelete: collaborator.permissions?.can_delete || false,
      role: collaborator.role
    }
  } catch (error) {
    logger.error('Error checking project access', error)
    return defaultPermissions
  }
}

/**
 * Check if a user can perform a specific action on a project
 */
export async function canPerformAction(
  userId: string,
  projectId: string,
  action: 'view' | 'edit' | 'manage_team' | 'export' | 'delete'
): Promise<boolean> {
  const permissions = await checkProjectAccess(userId, projectId)
  
  switch (action) {
    case 'view':
      return permissions.canView
    case 'edit':
      return permissions.canEdit
    case 'manage_team':
      return permissions.canManageTeam
    case 'export':
      return permissions.canExport
    case 'delete':
      return permissions.canDelete
    default:
      return false
  }
}