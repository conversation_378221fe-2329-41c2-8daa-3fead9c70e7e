import { logger } from '@/lib/services/logger'
'use client'

import { useState, useEffect } from 'react'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Mic, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Users,
  FileText
} from 'lucide-react'

interface VoiceMetric {
  character: string
  consistency: number
  trend: 'up' | 'down' | 'stable'
  samples: number
  lastAnalyzed?: string
}

interface VoiceConsistencyMetricsProps {
  projectId?: string
}

export function VoiceConsistencyMetrics({ projectId }: VoiceConsistencyMetricsProps) {
  const [metrics, setMetrics] = useState<VoiceMetric[]>([])
  const [overallConsistency, setOverallConsistency] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchVoiceMetrics()
  }, [projectId])

  const fetchVoiceMetrics = async () => {
    try {
      // Simulated data for now - would fetch from /api/analysis/voice-consistency
      const mockData: VoiceMetric[] = [
        {
          character: 'Protagonist',
          consistency: 92,
          trend: 'up',
          samples: 45,
          lastAnalyzed: '2 hours ago'
        },
        {
          character: 'Antagonist',
          consistency: 87,
          trend: 'stable',
          samples: 32,
          lastAnalyzed: '1 day ago'
        },
        {
          character: 'Narrator',
          consistency: 94,
          trend: 'up',
          samples: 120,
          lastAnalyzed: '30 minutes ago'
        }
      ]
      
      setMetrics(mockData)
      setOverallConsistency(
        Math.round(mockData.reduce((sum, m) => sum + m.consistency, 0) / mockData.length)
      )
    } catch (error) {
      logger.error('Failed to fetch voice metrics:', error)
    } finally {
      setLoading(false)
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return <Minus className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getConsistencyColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-blue-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-20 w-full" />
        {[1, 2, 3].map((i) => (
          <Skeleton key={i} className="h-16 w-full" />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overall Score */}
      <div className="text-center p-6 bg-muted rounded-lg">
        <Mic className="h-8 w-8 mx-auto mb-2 text-primary" />
        <div className="text-3xl font-bold mb-1">
          <span className={getConsistencyColor(overallConsistency)}>
            {overallConsistency}%
          </span>
        </div>
        <p className="text-sm text-muted-foreground">Overall Voice Consistency</p>
      </div>

      {/* Character Metrics */}
      <div className="space-y-4">
        <h4 className="font-medium flex items-center gap-2">
          <Users className="h-4 w-4" />
          Character Voice Analysis
        </h4>
        
        {metrics.map((metric) => (
          <div key={metric.character} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-medium">{metric.character}</span>
                {getTrendIcon(metric.trend)}
              </div>
              <div className="flex items-center gap-2">
                <span className={`font-semibold ${getConsistencyColor(metric.consistency)}`}>
                  {metric.consistency}%
                </span>
                <Badge variant="outline" className="text-xs">
                  <FileText className="h-3 w-3 mr-1" />
                  {metric.samples} samples
                </Badge>
              </div>
            </div>
            <Progress value={metric.consistency} className="h-2" />
            {metric.lastAnalyzed && (
              <p className="text-xs text-muted-foreground">
                Last analyzed: {metric.lastAnalyzed}
              </p>
            )}
          </div>
        ))}
      </div>

      {/* Tips */}
      <div className="p-4 bg-primary/5 rounded-lg">
        <p className="text-sm">
          <strong>Tip:</strong> Voice consistency above 85% indicates strong character 
          differentiation. Use the Voice Analyzer in your editor to improve scores.
        </p>
      </div>
    </div>
  )
}