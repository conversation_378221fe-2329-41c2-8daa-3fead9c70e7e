import { logger } from '../services/logger'

interface MailerooResponse {
  success: boolean
  message_id?: string
  error?: string
}

interface EmailOptions {
  to: string | string[]
  subject: string
  html: string
  text?: string
  from?: string
  replyTo?: string
  cc?: string[]
  bcc?: string[]
  attachments?: Array<{
    filename: string
    content: string // base64 encoded
    type: string
  }>
}

/**
 * Send email using Maileroo API
 * Documentation: https://maileroo.com/api-documentation/email-sending-api
 */
export async function sendMailerooEmail(options: EmailOptions): Promise<MailerooResponse> {
  const apiKey = process.env.MAILEROO_API_KEY
  
  if (!apiKey) {
    logger.error('MAILEROO_API_KEY not configured')
    throw new Error('Email service not configured')
  }

  const endpoint = 'https://smtp.maileroo.com/v1/send'
  
  try {
    const payload = {
      from: options.from || 'BookScribe AI <<EMAIL>>',
      to: Array.isArray(options.to) ? options.to : [options.to],
      subject: options.subject,
      html: options.html,
      text: options.text || stripHtml(options.html),
      reply_to: options.replyTo,
      cc: options.cc,
      bcc: options.bcc,
      attachments: options.attachments
    }

    // Remove undefined fields
    Object.keys(payload).forEach(key => {
      if (payload[key as keyof typeof payload] === undefined) {
        delete payload[key as keyof typeof payload]
      }
    })

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey
      },
      body: JSON.stringify(payload)
    })

    const data = await response.json()

    if (!response.ok) {
      logger.error('Maileroo API error', {
        status: response.status,
        error: data.error || data.message || 'Unknown error'
      })
      
      return {
        success: false,
        error: data.error || data.message || `HTTP ${response.status} error`
      }
    }

    logger.info('Email sent successfully via Maileroo', {
      to: options.to,
      subject: options.subject,
      messageId: data.message_id
    })

    return {
      success: true,
      message_id: data.message_id
    }

  } catch (error) {
    logger.error('Failed to send email via Maileroo', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send email'
    }
  }
}

/**
 * Strip HTML tags from content for text version
 */
function stripHtml(html: string): string {
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
    .replace(/<[^>]+>/g, ' ')
    .replace(/\s+/g, ' ')
    .trim()
}

/**
 * Validate email address format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Send collaboration invitation email
 */
export async function sendCollaborationInviteViaMaileroo(
  recipientEmail: string,
  projectTitle: string,
  inviterName: string,
  role: 'editor' | 'viewer',
  inviteUrl: string
): Promise<MailerooResponse> {
  const subject = `${inviterName} invited you to collaborate on "${projectTitle}"`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: #333333;
          background-color: #f5f5f5;
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
          background-color: #8B4513;
          color: #ffffff;
          padding: 40px 30px;
          text-align: center;
        }
        .header h1 {
          margin: 0;
          font-size: 28px;
          font-weight: 600;
        }
        .header p {
          margin: 10px 0 0 0;
          font-size: 16px;
          opacity: 0.9;
        }
        .content {
          padding: 40px 30px;
        }
        .content h2 {
          color: #8B4513;
          margin-top: 0;
          font-size: 24px;
        }
        .button {
          display: inline-block;
          padding: 14px 32px;
          background-color: #8B4513;
          color: #ffffff;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 600;
          font-size: 16px;
          margin: 24px 0;
          transition: background-color 0.3s;
        }
        .button:hover {
          background-color: #6B3410;
        }
        .features {
          background-color: #f9f7f5;
          padding: 24px;
          border-radius: 8px;
          margin: 24px 0;
        }
        .features h3 {
          margin-top: 0;
          color: #8B4513;
          font-size: 18px;
        }
        .features ul {
          margin: 0;
          padding-left: 24px;
          color: #555555;
        }
        .features li {
          margin-bottom: 8px;
        }
        .footer {
          background-color: #f5f5f5;
          padding: 30px;
          text-align: center;
          font-size: 14px;
          color: #666666;
        }
        .footer a {
          color: #8B4513;
          text-decoration: none;
        }
        @media only screen and (max-width: 600px) {
          .content {
            padding: 30px 20px;
          }
          .header {
            padding: 30px 20px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>BookScribe AI</h1>
          <p>AI-Powered Novel Writing Platform</p>
        </div>
        
        <div class="content">
          <h2>You're invited to collaborate! ✨</h2>
          
          <p>Hi there,</p>
          
          <p><strong>${inviterName}</strong> has invited you to collaborate on their novel <strong>"${projectTitle}"</strong> as ${role === 'editor' ? 'an' : 'a'} <strong>${role}</strong>.</p>
          
          <div class="features">
            <h3>As ${role === 'editor' ? 'an' : 'a'} ${role}, you'll be able to:</h3>
            ${role === 'editor' ? `
              <ul>
                <li>📝 View and edit all chapters in real-time</li>
                <li>📚 Access the complete Story Bible</li>
                <li>🤖 Use AI-powered writing tools</li>
                <li>📊 Track character development and plot threads</li>
                <li>💾 Export the manuscript in multiple formats</li>
                <li>👥 Collaborate with other team members</li>
              </ul>
            ` : `
              <ul>
                <li>📖 Read all chapters and story content</li>
                <li>📚 View the Story Bible and character profiles</li>
                <li>💬 Leave comments and feedback</li>
                <li>💾 Export the manuscript for offline reading</li>
                <li>📊 View project analytics and progress</li>
              </ul>
            `}
          </div>
          
          <div style="text-align: center;">
            <a href="${inviteUrl}" class="button">Accept Invitation</a>
          </div>
          
          <p><strong>⏰ Important:</strong> This invitation will expire in 7 days. If you don't have a BookScribe account yet, you'll be prompted to create one when you accept.</p>
          
          <p>If you have any questions, feel free to reach out to ${inviterName} directly.</p>
          
          <p>Happy writing!</p>
          <p>The BookScribe Team</p>
        </div>
        
        <div class="footer">
          <p>This invitation was sent to ${recipientEmail}.</p>
          <p>If you didn't expect this invitation, you can safely ignore this email.</p>
          <p>&copy; ${new Date().getFullYear()} BookScribe AI. All rights reserved.</p>
          <p><a href="https://bookscribe.ai/privacy">Privacy Policy</a> | <a href="https://bookscribe.ai/terms">Terms of Service</a></p>
        </div>
      </div>
    </body>
    </html>
  `
  
  return sendMailerooEmail({
    to: recipientEmail,
    subject,
    html,
    from: 'BookScribe AI <<EMAIL>>',
    replyTo: '<EMAIL>'
  })
}