/**
 * Simple Operational Transformation implementation for text editing
 * This handles basic insert/delete operations with conflict resolution
 */

import type { editor } from 'monaco-editor'

export interface Operation {
  type: 'insert' | 'delete'
  position: number
  text?: string // For insert operations
  length?: number // For delete operations
  userId: string
  timestamp: number
}

export interface TransformResult {
  op1: Operation
  op2: Operation
}

/**
 * Transform two concurrent operations so they can be applied in sequence
 * Returns transformed versions of both operations
 */
export function transformOperations(op1: Operation, op2: Operation): TransformResult {
  // op1 is "our" operation, op2 is "their" operation
  // We need to transform op2 to account for op1 being applied first
  
  if (op1.type === 'insert' && op2.type === 'insert') {
    return transformInsertInsert(op1, op2)
  } else if (op1.type === 'insert' && op2.type === 'delete') {
    return transformInsertDelete(op1, op2)
  } else if (op1.type === 'delete' && op2.type === 'insert') {
    return transformDeleteInsert(op1, op2)
  } else if (op1.type === 'delete' && op2.type === 'delete') {
    return transformDeleteDelete(op1, op2)
  }
  
  // Default: return unchanged
  return { op1, op2 }
}

function transformInsertInsert(op1: Operation, op2: Operation): TransformResult {
  if (op1.position < op2.position || 
      (op1.position === op2.position && op1.userId < op2.userId)) {
    // op1 comes first, shift op2 position
    return {
      op1,
      op2: {
        ...op2,
        position: op2.position + (op1.text?.length || 0)
      }
    }
  } else {
    // op2 comes first, shift op1 position
    return {
      op1: {
        ...op1,
        position: op1.position + (op2.text?.length || 0)
      },
      op2
    }
  }
}

function transformInsertDelete(op1: Operation, op2: Operation): TransformResult {
  const insertPos = op1.position
  const deleteStart = op2.position
  const deleteEnd = deleteStart + (op2.length || 0)
  const insertLength = op1.text?.length || 0
  
  if (insertPos <= deleteStart) {
    // Insert before delete region, shift delete position
    return {
      op1,
      op2: {
        ...op2,
        position: op2.position + insertLength
      }
    }
  } else if (insertPos >= deleteEnd) {
    // Insert after delete region, shift insert position back
    return {
      op1: {
        ...op1,
        position: op1.position - (op2.length || 0)
      },
      op2
    }
  } else {
    // Insert within delete region, split the delete
    return {
      op1: {
        ...op1,
        position: deleteStart
      },
      op2: {
        ...op2,
        length: (op2.length || 0) + insertLength
      }
    }
  }
}

function transformDeleteInsert(op1: Operation, op2: Operation): TransformResult {
  const result = transformInsertDelete(
    { ...op2, type: 'insert' } as Operation,
    { ...op1, type: 'delete' } as Operation
  )
  
  return {
    op1: { ...result.op2, type: 'delete' },
    op2: { ...result.op1, type: 'insert' }
  }
}

function transformDeleteDelete(op1: Operation, op2: Operation): TransformResult {
  const start1 = op1.position
  const end1 = start1 + (op1.length || 0)
  const start2 = op2.position
  const end2 = start2 + (op2.length || 0)
  
  if (end1 <= start2) {
    // op1 entirely before op2, shift op2 back
    return {
      op1,
      op2: {
        ...op2,
        position: op2.position - (op1.length || 0)
      }
    }
  } else if (end2 <= start1) {
    // op2 entirely before op1, shift op1 back
    return {
      op1: {
        ...op1,
        position: op1.position - (op2.length || 0)
      },
      op2
    }
  } else if (start1 <= start2 && end1 >= end2) {
    // op1 contains op2, reduce op1 length
    return {
      op1: {
        ...op1,
        length: (op1.length || 0) - (op2.length || 0)
      },
      op2: {
        ...op2,
        position: start1,
        length: 0
      }
    }
  } else if (start2 <= start1 && end2 >= end1) {
    // op2 contains op1, reduce op2 length
    return {
      op1: {
        ...op1,
        position: start2,
        length: 0
      },
      op2: {
        ...op2,
        length: (op2.length || 0) - (op1.length || 0)
      }
    }
  } else if (start1 < start2 && end1 > start2) {
    // op1 overlaps start of op2
    const overlap = end1 - start2
    return {
      op1: {
        ...op1,
        length: (op1.length || 0) - overlap
      },
      op2: {
        ...op2,
        position: start1,
        length: (op2.length || 0) - overlap
      }
    }
  } else {
    // op2 overlaps start of op1
    const overlap = end2 - start1
    return {
      op1: {
        ...op1,
        position: start2,
        length: (op1.length || 0) - overlap
      },
      op2: {
        ...op2,
        length: (op2.length || 0) - overlap
      }
    }
  }
}

/**
 * Apply an operation to a text string
 */
export function applyOperation(text: string, operation: Operation): string {
  if (operation.type === 'insert') {
    const before = text.substring(0, operation.position)
    const after = text.substring(operation.position)
    return before + (operation.text || '') + after
  } else if (operation.type === 'delete') {
    const before = text.substring(0, operation.position)
    const after = text.substring(operation.position + (operation.length || 0))
    return before + after
  }
  
  return text
}

/**
 * Convert Monaco editor change to OT operation
 */
export function monacoChangeToOperation(
  change: editor.IModelContentChange,
  userId: string,
  documentLength: number
): Operation {
  const startOffset = getOffsetFromPosition(
    change.range.startLineNumber,
    change.range.startColumn,
    documentLength
  )
  
  if (change.text) {
    if (change.rangeLength > 0) {
      // Replace operation = delete + insert
      // For simplicity, we'll just handle as insert
      return {
        type: 'insert',
        position: startOffset,
        text: change.text,
        userId,
        timestamp: Date.now()
      }
    } else {
      // Pure insert
      return {
        type: 'insert',
        position: startOffset,
        text: change.text,
        userId,
        timestamp: Date.now()
      }
    }
  } else {
    // Delete operation
    return {
      type: 'delete',
      position: startOffset,
      length: change.rangeLength,
      userId,
      timestamp: Date.now()
    }
  }
}

/**
 * Convert line/column position to character offset
 * This is a simplified version - in production you'd need the actual document
 */
function getOffsetFromPosition(
  line: number,
  column: number,
  documentLength: number
): number {
  // This is a placeholder - in reality you'd need to calculate based on line breaks
  // For now, we'll use a simple approximation
  return Math.min((line - 1) * 80 + (column - 1), documentLength)
}

/**
 * Merge multiple operations into a single operation where possible
 */
export function mergeOperations(operations: Operation[]): Operation[] {
  if (operations.length <= 1) return operations
  
  const merged: Operation[] = []
  let current = operations[0]
  
  for (let i = 1; i < operations.length; i++) {
    const next = operations[i]
    
    // Try to merge consecutive operations from the same user
    if (current.userId === next.userId && 
        current.type === 'insert' && 
        next.type === 'insert' &&
        current.position + (current.text?.length || 0) === next.position) {
      // Merge consecutive inserts
      current = {
        ...current,
        text: (current.text || '') + (next.text || ''),
        timestamp: next.timestamp
      }
    } else if (current.userId === next.userId && 
               current.type === 'delete' && 
               next.type === 'delete' &&
               current.position === next.position) {
      // Merge consecutive deletes at same position
      current = {
        ...current,
        length: (current.length || 0) + (next.length || 0),
        timestamp: next.timestamp
      }
    } else {
      // Can't merge, save current and move to next
      merged.push(current)
      current = next
    }
  }
  
  merged.push(current)
  return merged
}