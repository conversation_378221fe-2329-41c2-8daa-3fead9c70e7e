import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { requireAuth, requireProjectAccess } from '@/lib/api/auth-middleware'
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator'
import { WritingAgent } from '@/lib/agents/writing-agent'
import { ChapterGenerator } from '@/lib/services/chapter-generator'
import type { ChapterContext } from '@/lib/services/chapter-generator'
import { getClientIP } from '@/lib/rate-limiter'
import { AI_RATE_LIMITS, createAIRateLimitResponse } from '@/lib/rate-limiter-ai'
import { checkUsageBeforeAction, trackUsage } from '@/lib/usage-tracker'
import { z } from 'zod'
import { ComprehensiveStoryGenerator } from '@/lib/services/comprehensive-story-generator'
import { bookContextBuilder } from '@/lib/utils/book-context-builder'
import { createBatchProcessor } from '@/lib/services/batch-chapter-processor'
import { EditorAgent } from '@/lib/agents/editor-agent'
import { ContentGenerator } from '@/lib/services/content-generator'
import { logger } from '@/lib/services/logger'
import { validateModelAccess, canPerformAction, getUpgradeMessage } from '@/lib/services/model-access-validator'

// Validation schemas
const generateRequestSchema = z.object({
  projectId: z.string().uuid(),
  action: z.enum(['generate_structure', 'generate_chapter', 'expand_chapter', 'edit_chapter', 'write_chapter', 'generate_chapter_enhanced', 'generate_complete_story', 'batch_write_chapters', 'regenerate_part']),
  chapterNumber: z.number().int().positive().optional(),
  chapterNumbers: z.array(z.number().int().positive()).optional(),
  editInstructions: z.string().optional(),
  regenerateOption: z.enum(['structure', 'characters', 'events', 'chapter']).optional(),
  options: z.object({
    includeCharacters: z.boolean().optional(),
    includeWorldBuilding: z.boolean().optional(),
    includeStoryBible: z.boolean().optional(),
    regenerateExisting: z.boolean().optional(),
    maxConcurrent: z.number().int().min(1).max(5).optional(),
    expansionFactor: z.number().min(1.2).max(3).optional(),
    targetWordCount: z.number().int().positive().optional()
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting check - stricter for expensive AI operations
    const clientIP = getClientIP(request)
    const { limiter, requests } = AI_RATE_LIMITS.generation
    const rateLimitResult = limiter.check(requests, clientIP)
    
    if (!rateLimitResult.success) {
      return createAIRateLimitResponse(rateLimitResult.reset)
    }
    
    // Check authentication using the standard middleware
    const authResult = await requireAuth(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }
    const user = authResult

    // Check usage limits before proceeding
    const usageCheck = await checkUsageBeforeAction(user.id, 'ai_generation')
    if (!usageCheck.allowed) {
      return NextResponse.json({ 
        error: usageCheck.reason,
        upgradeRequired: true,
        usage: usageCheck.usage,
        limits: usageCheck.limits
      }, { status: 403 })
    }
    
    // Get user subscription tier for model access validation
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single()
    
    const userTier = subscription?.tier || 'free'

    const body = await request.json()
    
    // Validate request body
    const validationResult = generateRequestSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: validationResult.error.errors 
      }, { status: 400 })
    }
    
    const { projectId, action } = validationResult.data
    
    // Validate action is allowed for user's tier
    if (!canPerformAction(userTier, action)) {
      const requiredTier = action === 'generate_complete_story' || action === 'batch_write_chapters' 
        ? 'professional' 
        : 'starter'
      
      return NextResponse.json({
        error: `This action requires ${requiredTier} tier or higher`,
        message: getUpgradeMessage(userTier, requiredTier, action),
        currentTier: userTier,
        requiredTier,
        upgradeUrl: '/pricing'
      }, { status: 403 })
    }

    // Check project access using the standard middleware
    const projectAuthResult = await requireProjectAccess(request, projectId, 'editor')
    if (projectAuthResult instanceof NextResponse) {
      return projectAuthResult
    }

    // Get project from database  
    const supabase = await createClient()
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    switch (action) {
      case 'generate_complete_story': {
        logger.info('Generating complete story with comprehensive generator...')

        try {
          const generator = new ComprehensiveStoryGenerator()
          const result = await generator.generateCompleteStory({
            projectId,
            action: 'generate_complete_story',
            options: validationResult.data.options
          })

          return NextResponse.json({
            success: true,
            data: result,
            message: 'Complete story generated successfully'
          })
        } catch (error) {
          logger.error('Error generating complete story:', error)
          return NextResponse.json({
            success: false,
            error: 'Failed to generate complete story',
            details: error instanceof Error ? error.message : String(error)
          }, { status: 500 })
        }
      }

      case 'generate_structure': {
        logger.info('Generating complete project structure using Advanced Orchestrator...')

        try {
          // Build project settings from the project data
          const projectSettings = {
            genre: project.primary_genre,
            subGenres: project.sub_genres || [],
            povType: project.pov_type || 'third_limited',
            narrativeVoice: project.narrative_voice || 'standard',
            tense: project.tense || 'past',
            worldType: project.world_type || 'real_world',
            timePeriod: project.time_period || 'contemporary',
            magicTechLevel: project.magic_tech_level || 'none',
            violenceLevel: project.violence_level || 'moderate',
            romanceLevel: project.romance_level || 'minimal',
            humorLevel: project.humor_level || 'moderate',
            tone: project.tone_options || ['serious'],
            pacing: project.pacing || 'moderate',
            dialogueDensity: project.dialogue_density || 'moderate',
            descriptiveDensity: project.descriptive_density || 'moderate',
            narrativeDistance: project.narrative_distance || 'medium',
            chapterStructure: project.chapter_structure || 'standard',
            sceneDensity: project.scene_density || '3-5',
            conflictTypes: project.conflict_types || ['internal', 'external'],
            endings: project.endings || ['conclusive'],
            writingStyle: project.writing_style || 'standard',
            storyThemes: project.themes || [],
            protagonistTypes: project.protagonist_types || ['hero'],
            antagonistTypes: project.antagonist_types || ['villain'],
            majorThemes: project.themes || [],
            primaryGenre: project.primary_genre,
            pacingPreference: project.pacing || 'moderate'
          }

          // Use the advanced orchestrator for comprehensive generation
          const orchestrator = new AdvancedAgentOrchestrator()
          const result = await orchestrator.orchestrateProject(
            projectId,
            projectSettings,
            project.description || project.title,
            project.target_word_count || 80000,
            project.target_chapters || 20
          )

          if (!result.success || !result.data) {
            throw new Error(result.error || 'Orchestration failed')
          }

          const bookContext = result.data

          // Store the generated data in the database
          logger.info('Storing generated structure in database...')

          // Store story arcs
          if (bookContext.storyStructure?.acts) {
            await supabase.from('story_arcs').delete().eq('project_id', projectId)

            for (const act of bookContext.storyStructure.acts) {
              await supabase.from('story_arcs').insert({
                project_id: projectId,
                act_number: act.number,
                description: act.description,
                key_events: act.keyEvents || []
              })
            }
          }

          // Store characters
          if (bookContext.characters) {
            await supabase.from('characters').delete().eq('project_id', projectId)

            const allCharacters = [
              ...(bookContext.characters.protagonists || []),
              ...(bookContext.characters.antagonists || []),
              ...(bookContext.characters.supporting || [])
            ]

            for (const character of allCharacters) {
              await supabase.from('characters').insert({
                project_id: projectId,
                name: character.name,
                role: character.role,
                description: character.description,
                backstory: character.backstory || '',
                personality_traits: character.personality || { traits: [] },
                character_arc: character.arc || { type: 'flat_arc' },
                relationships: character.relationships || [],
                physical_description: character.appearance || ''
              })
            }
          }

          // Store chapter outlines - respect user's target chapters
          if (bookContext.chapterOutlines?.chapters) {
            await supabase.from('chapters').delete().eq('project_id', projectId)

            const wordsPerChapter = Math.floor(project.target_word_count / project.target_chapters)

            for (const chapterOutline of bookContext.chapterOutlines.chapters) {
              await supabase.from('chapters').insert({
                project_id: projectId,
                chapter_number: chapterOutline.number,
                title: chapterOutline.title,
                target_word_count: chapterOutline.wordCountTarget || wordsPerChapter,
                outline: JSON.stringify(chapterOutline),
                status: 'planned'
              })
            }
          }

          // Store story bible entries
          if (bookContext.storyBible) {
            await supabase.from('story_bible').delete().eq('project_id', projectId)

            // Store world rules
            if (bookContext.storyBible.worldRules?.length > 0) {
              for (let i = 0; i < bookContext.storyBible.worldRules.length; i++) {
                await supabase.from('story_bible').insert({
                  project_id: projectId,
                  entry_type: 'world_rule',
                  entry_key: `rule_${i}`,
                  entry_data: { value: bookContext.storyBible.worldRules[i] }
                })
              }
            }

            // Store timeline events
            if (bookContext.storyStructure?.timeline?.length > 0) {
              for (const event of bookContext.storyStructure.timeline) {
                await supabase.from('story_bible').insert({
                  project_id: projectId,
                  entry_type: 'timeline_event',
                  entry_key: event.id || `event_${event.chapterNumber}`,
                  entry_data: event
                })
              }
            }

            // Store themes
            if (bookContext.storyStructure?.themes?.length > 0) {
              await supabase.from('story_bible').insert({
                project_id: projectId,
                entry_type: 'themes',
                entry_key: 'main_themes',
                entry_data: { themes: bookContext.storyStructure.themes }
              })
            }
          }

          // Update project status
          await supabase
            .from('projects')
            .update({ status: 'writing' })
            .eq('id', projectId)

          // Track usage
          await trackUsage({
            userId: user.id,
            eventType: 'ai_generation',
            metadata: { action: 'generate_structure', projectId }
          })

          logger.info('Project structure generated successfully with advanced orchestrator!')

          return NextResponse.json({
            success: true,
            data: {
              storyStructure: bookContext.storyStructure,
              characters: bookContext.characters,
              chapterOutlines: bookContext.chapterOutlines,
              storyBible: bookContext.storyBible,
              message: 'Comprehensive project structure generated successfully'
            },
            message: 'Project structure generated successfully'
          })

        } catch (error) {
          logger.error('Advanced structure generation failed:', error)
          return NextResponse.json({
            error: 'Failed to generate project structure',
            details: error instanceof Error ? error.message : String(error)
          }, { status: 500 })
        }
      }

      case 'write_chapter': {
        const { chapterNumber } = validationResult.data
        logger.info(`Writing chapter ${chapterNumber}...`)

        // Build context using utility
        const context = await bookContextBuilder.buildFromProjectId(projectId, user.id)
        
        if (!context.storyStructure || !context.characters || !context.storyBible) {
          return NextResponse.json({ error: 'Project structure not found. Please generate structure first.' }, { status: 400 })
        }
        
        // Find or create chapter outline
        const { data: chapters } = await supabase
          .from('chapters')
          .select('*')
          .eq('project_id', projectId)
          .order('chapter_number')
        
        const existingChapter = chapters?.find(ch => ch.chapter_number === chapterNumber)
        
        if (!existingChapter?.outline) {
          return NextResponse.json({ error: 'Chapter outline not found. Please generate project structure first.' }, { status: 400 })
        }

        const chapterOutline = JSON.parse(existingChapter.outline)
        
        // Get previous chapter for context (currently not used in orchestrator)
        // const previousChapter = chapters?.find(ch => ch.chapter_number === chapterNumber - 1)

        // Use the writing agent directly
        const writingAgent = new WritingAgent(context)
        const chapterContent = await writingAgent.writeChapter(chapterOutline)

        // Store the chapter in database
        await supabase
          .from('chapters')
          .update({
            content: chapterContent.content,
            actual_word_count: chapterContent.wordCount,
            status: 'complete',
            ai_notes: { characterVoices: chapterContent.characterVoices, continuityNotes: chapterContent.continuityNotes }
          })
          .eq('project_id', projectId)
          .eq('chapter_number', chapterNumber)

        // Update project word count
        await supabase
          .from('projects')
          .update({
            current_word_count: (project.current_word_count || 0) + chapterContent.wordCount
          })
          .eq('id', projectId)

        // Track usage
        await trackUsage({
          userId: user.id,
          eventType: 'ai_generation',
          metadata: { action: 'write_chapter', projectId, chapterNumber }
        })

        return NextResponse.json({
          success: true,
          data: chapterContent,
          message: `Chapter ${chapterNumber} written successfully`
        })
      }

      case 'generate_chapter_enhanced': {
        const { chapterNumber } = validationResult.data
        logger.info(`Enhanced chapter generation for chapter ${chapterNumber}...`)

        // Build context using utility
        const context = await bookContextBuilder.buildFromProjectId(projectId, user.id)
        
        if (!context.storyStructure || !context.characters || !context.storyBible) {
          return NextResponse.json({ error: 'Project structure not found. Please generate structure first.' }, { status: 400 })
        }
        
        // Get chapter data from database since we need the outline
        const { data: chaptersData } = await supabase
          .from('chapters')
          .select('*')
          .eq('project_id', projectId)
          .eq('chapter_number', chapterNumber)
          .single()

        if (!chaptersData?.outline) {
          return NextResponse.json({ error: 'Chapter outline not found. Please generate project structure first.' }, { status: 400 })
        }

        const chapterOutline = JSON.parse(chaptersData.outline)
        
        // Build enhanced chapter context
        const chapterContext: ChapterContext = {
          projectId,
          chapterNumber: chapterNumber ?? 1,
          chapterOutline,
          projectSettings: {
            primaryGenre: context.settings?.genre || project.primary_genre,
            narrativeVoice: context.settings?.narrativeVoice || project.narrative_voice,
            tense: context.settings?.tense || project.tense,
            writingStyle: context.settings?.writingStyle || project.writing_style,
            tone: context.settings?.tone || project.tone_options || [],
            targetAudience: project.target_audience,
            contentRating: project.content_rating
          },
          characters: [
            ...(context.characters?.protagonists || []),
            ...(context.characters?.antagonists || []),
            ...(context.characters?.supporting || [])
          ].map(char => ({
            id: char.id,
            name: char.name,
            role: char.role,
            description: char.description,
            personality: char.personality,
            voiceCharacteristics: char.voice?.mannerisms || []
          })),
          storyBible: {
            worldRules: context.storyBible?.worldRules?.reduce((acc, rule, index) => {
              acc[`rule_${index}`] = rule;
              return acc;
            }, {} as Record<string, string>) || {},
            timeline: context.storyBible?.structure.timeline.map(event => ({
              event: event.event,
              chapter: event.chapter || event.chapterNumber || 1
            })) || [],
            plotThreads: []
          },
          previousChapters: (context.completedChapters || [])
            .filter(ch => ch.chapterNumber < (chapterNumber ?? 1))
            .map(ch => ({
              chapterNumber: ch.chapterNumber,
              content: ch.content,
              wordCount: ch.wordCount || 0,
              characterVoices: {},
              summary: `Chapter ${ch.chapterNumber}: ${ch.title}`
            }))
        }

        // Use enhanced chapter generator
        const chapterGenerator = new ChapterGenerator()
        await chapterGenerator.initialize()

        const result = await chapterGenerator.generateChapter(chapterContext)

        if (!result.success || !result.data) {
          logger.error('Enhanced chapter generation failed:', result.error)
          return NextResponse.json({ error: result.error || 'Chapter generation failed' }, { status: 500 })
        }

        const generatedChapter = result.data

        // Store the generated chapter (but don't mark as complete yet - user needs to review)
        await supabase
          .from('chapters')
          .update({
            content: generatedChapter.content,
            actual_word_count: generatedChapter.wordCount,
            status: 'generated', // New status for AI-generated content awaiting review
            ai_notes: {
              characterVoices: generatedChapter.characterVoices,
              continuityNotes: generatedChapter.continuityNotes,
              plotProgression: generatedChapter.plotProgression,
              qualityScore: generatedChapter.qualityScore,
              generationMetadata: generatedChapter.generationMetadata,
              scenes: generatedChapter.scenes
            }
          })
          .eq('project_id', projectId)
          .eq('chapter_number', chapterNumber)

        // Track usage
        await trackUsage({
          userId: user.id,
          eventType: 'ai_generation',
          metadata: { action: 'generate_chapter_enhanced', projectId, chapterNumber }
        })

        return NextResponse.json({
          success: true,
          data: generatedChapter,
          message: `Chapter ${chapterNumber} generated successfully - ready for review`
        })
      }

      case 'batch_write_chapters': {
        const { chapterNumbers, options } = validationResult.data
        
        if (!chapterNumbers || chapterNumbers.length === 0) {
          return NextResponse.json({ error: 'Chapter numbers array is required for batch processing' }, { status: 400 })
        }

        logger.info(`Starting batch chapter generation for ${chapterNumbers.length} chapters...`)

        // Build context
        const context = await bookContextBuilder.buildFromProjectId(projectId, user.id)
        
        if (!context.storyStructure || !context.characters || !context.chapterOutlines) {
          return NextResponse.json({ error: 'Project structure not found. Please generate structure first.' }, { status: 400 })
        }

        // Get chapter outlines for requested chapters
        const chapterOutlines = chapterNumbers
          .map(num => context.chapterOutlines?.chapters.find(ch => ch.number === num))
          .filter((ch): ch is NonNullable<typeof ch> => ch !== undefined);

        if (chapterOutlines.length === 0) {
          return NextResponse.json({ error: 'No valid chapter outlines found for requested chapters' }, { status: 400 })
        }

        // Create batch processor
        const batchProcessor = createBatchProcessor(
          context,
          options?.maxConcurrent
        );

        // Add chapters to batch
        batchProcessor.addChapters(chapterOutlines);

        // Set up progress tracking
        const progressUpdates: Record<string, unknown>[] = [];
        
        batchProcessor.on('task:completed', (data) => {
          progressUpdates.push({ type: 'completed', ...data });
        });

        batchProcessor.on('task:failed', (data) => {
          progressUpdates.push({ type: 'failed', ...data });
        });

        // Process batch
        const _startTime = Date.now();
        const result = await batchProcessor.processBatch();

        // Store successful chapters in database
        for (const chapter of result.successful) {
          await supabase
            .from('chapters')
            .update({
              content: chapter.content,
              actual_word_count: chapter.wordCount,
              updated_at: new Date().toISOString(),
              ai_notes: {
                scenes: chapter.scenes,
                characterVoices: chapter.characterVoices,
                themes: chapter.themes,
                continuityNotes: chapter.continuityNotes,
                continuityWarnings: chapter.continuityWarnings
              }
            })
            .eq('project_id', projectId)
            .eq('chapter_number', chapter.chapterNumber)
        }

        // Update project word count
        const totalNewWords = result.successful.reduce((sum, ch) => sum + ch.wordCount, 0);
        await supabase
          .from('projects')
          .update({
            current_word_count: project.current_word_count + totalNewWords,
            updated_at: new Date().toISOString()
          })
          .eq('id', projectId)

        // Track usage
        await trackUsage({
          userId: user.id,
          eventType: 'ai_generation',
          metadata: {
            action: 'batch_write_chapters',
            projectId,
            chaptersGenerated: result.successful.length,
            chaptersFailed: result.failed.length,
            totalWords: totalNewWords,
            processingTime: result.totalTime,
            qualityMetrics: result.qualityMetrics
          },
          amount: result.successful.length // Track each chapter as one generation
        })

        return NextResponse.json({
          success: true,
          data: {
            successful: result.successful.length,
            failed: result.failed.length,
            chapters: result.successful.map(ch => ({
              chapterNumber: ch.chapterNumber,
              title: ch.title,
              wordCount: ch.wordCount
            })),
            failures: result.failed,
            totalWords: totalNewWords,
            processingTime: result.totalTime,
            qualityMetrics: result.qualityMetrics,
            progressLog: progressUpdates
          },
          message: `Successfully generated ${result.successful.length} of ${chapterNumbers.length} chapters`
        })
      }

      case 'edit_chapter': {
        const { chapterNumber, editInstructions } = validationResult.data
        
        if (!chapterNumber) {
          return NextResponse.json({ error: 'Chapter number is required for editing' }, { status: 400 })
        }
        
        if (!editInstructions) {
          return NextResponse.json({ error: 'Edit instructions are required' }, { status: 400 })
        }

        logger.info(`Editing chapter ${chapterNumber} with instructions...`)

        try {
          // Get the existing chapter
          const { data: chapter } = await supabase
            .from('chapters')
            .select('*')
            .eq('project_id', projectId)
            .eq('chapter_number', chapterNumber)
            .single()

          if (!chapter || !chapter.content) {
            return NextResponse.json({ error: 'Chapter not found or has no content to edit' }, { status: 404 })
          }

          // Build context for the editor
          const context = await bookContextBuilder.buildFromProjectId(projectId, user.id)
          
          // Create editor agent and perform review
          const editorAgent = new EditorAgent(context)
          const editorialReview = await editorAgent.executeReview({
            chapterNumber,
            title: chapter.title,
            content: chapter.content,
            wordCount: chapter.actual_word_count || 0,
            scenes: [],
            characterVoices: [],
            themes: [],
            continuityNotes: []
          })

          // Apply edits using the content generator with specific instructions
          const contentGenerator = new ContentGenerator()
          await contentGenerator.initialize()

          const editPrompt = `
Here is chapter ${chapterNumber} that needs editing:

${chapter.content}

Editorial Review:
${JSON.stringify(editorialReview, null, 2)}

Specific Edit Instructions from User:
${editInstructions}

Please rewrite this chapter incorporating:
1. All the editorial suggestions
2. The specific user instructions
3. Maintaining consistency with the story bible and character voices
4. Keeping approximately the same word count (${chapter.actual_word_count} words)

Provide the complete edited chapter text.`

          const editResult = await contentGenerator.generateContent({
            type: 'chapter',
            prompt: editPrompt,
            context: {
              chapterNumber,
              originalWordCount: chapter.actual_word_count,
              editorialFeedback: editorialReview
            },
            length: 'long',
            projectId
          })

          if (!editResult.success || !editResult.data) {
            throw new Error('Failed to generate edited content')
          }

          const editedContent = editResult.data
          const editedWordCount = editedContent.split(/\s+/).filter(word => word.length > 0).length

          // Update the chapter with edited content
          await supabase
            .from('chapters')
            .update({
              content: editedContent,
              actual_word_count: editedWordCount,
              status: 'edited',
              ai_notes: {
                ...chapter.ai_notes,
                lastEditInstructions: editInstructions,
                editorialReview: editorialReview,
                editedAt: new Date().toISOString()
              }
            })
            .eq('project_id', projectId)
            .eq('chapter_number', chapterNumber)

          // Track usage
          await trackUsage({
            userId: user.id,
            eventType: 'ai_generation',
            metadata: { action: 'edit_chapter', projectId, chapterNumber }
          })

          return NextResponse.json({
            success: true,
            data: {
              content: editedContent,
              wordCount: editedWordCount,
              editorialReview
            },
            message: `Chapter ${chapterNumber} edited successfully`
          })

        } catch (error) {
          logger.error('Chapter editing failed:', error)
          return NextResponse.json({
            error: 'Failed to edit chapter',
            details: error instanceof Error ? error.message : String(error)
          }, { status: 500 })
        }
      }

      case 'expand_chapter': {
        const { chapterNumber, options } = validationResult.data
        
        if (!chapterNumber) {
          return NextResponse.json({ error: 'Chapter number is required for expansion' }, { status: 400 })
        }

        const expansionFactor = options?.expansionFactor || 1.5
        const targetWordCount = options?.targetWordCount

        logger.info(`Expanding chapter ${chapterNumber} by ${expansionFactor}x...`)

        try {
          // Get the existing chapter
          const { data: chapter } = await supabase
            .from('chapters')
            .select('*')
            .eq('project_id', projectId)
            .eq('chapter_number', chapterNumber)
            .single()

          if (!chapter || !chapter.content) {
            return NextResponse.json({ error: 'Chapter not found or has no content to expand' }, { status: 404 })
          }

          const currentWordCount = chapter.actual_word_count || 0
          const expansionTarget = targetWordCount || Math.floor(currentWordCount * expansionFactor)

          // Build context
          const context = await bookContextBuilder.buildFromProjectId(projectId, user.id)

          // Parse the chapter outline if available
          let chapterOutline
          try {
            chapterOutline = chapter.outline ? JSON.parse(chapter.outline) : null
          } catch {
            chapterOutline = null
          }

          // Create expansion prompt
          const expansionPrompt = `
Here is chapter ${chapterNumber} that needs to be expanded:

${chapter.content}

Current word count: ${currentWordCount}
Target word count: ${expansionTarget}

${chapterOutline ? `Chapter Outline:\n${JSON.stringify(chapterOutline, null, 2)}` : ''}

Please expand this chapter to approximately ${expansionTarget} words by:
1. Adding more sensory details and descriptions
2. Expanding dialogue with subtext and character reactions
3. Deepening character introspection and emotional responses
4. Adding scene-setting details and atmosphere
5. Incorporating subtle foreshadowing or callbacks
6. Enriching action sequences with more detail
7. Expanding transitions between scenes

Maintain the same plot points and events, but add depth and richness to the prose.
Ensure the expansion feels natural and enhances the story rather than padding it.

Provide the complete expanded chapter text.`

          const contentGenerator = new ContentGenerator()
          await contentGenerator.initialize()

          const expansionResult = await contentGenerator.generateContent({
            type: 'chapter',
            prompt: expansionPrompt,
            context: {
              chapterNumber,
              currentWordCount,
              targetWordCount: expansionTarget,
              storyContext: context
            },
            length: 'long',
            projectId
          })

          if (!expansionResult.success || !expansionResult.data) {
            throw new Error('Failed to generate expanded content')
          }

          const expandedContent = expansionResult.data
          const expandedWordCount = expandedContent.split(/\s+/).filter(word => word.length > 0).length

          // Update the chapter with expanded content
          await supabase
            .from('chapters')
            .update({
              content: expandedContent,
              actual_word_count: expandedWordCount,
              status: 'expanded',
              ai_notes: {
                ...chapter.ai_notes,
                expansionFactor,
                originalWordCount: currentWordCount,
                expandedAt: new Date().toISOString()
              }
            })
            .eq('project_id', projectId)
            .eq('chapter_number', chapterNumber)

          // Track usage
          await trackUsage({
            userId: user.id,
            eventType: 'ai_generation',
            metadata: { action: 'expand_chapter', projectId, chapterNumber, expansionFactor }
          })

          return NextResponse.json({
            success: true,
            data: {
              content: expandedContent,
              wordCount: expandedWordCount,
              originalWordCount: currentWordCount,
              expansionRatio: expandedWordCount / currentWordCount
            },
            message: `Chapter ${chapterNumber} expanded successfully`
          })

        } catch (error) {
          logger.error('Chapter expansion failed:', error)
          return NextResponse.json({
            error: 'Failed to expand chapter',
            details: error instanceof Error ? error.message : String(error)
          }, { status: 500 })
        }
      }

      case 'regenerate_part': {
        const { regenerateOption, chapterNumber } = validationResult.data
        
        if (!regenerateOption) {
          return NextResponse.json({ error: 'Regenerate option is required' }, { status: 400 })
        }

        logger.info(`Regenerating ${regenerateOption}...`)

        try {
          switch (regenerateOption) {
            case 'structure':
              // Regenerate story structure using the comprehensive generator
              const structureGen = new ComprehensiveStoryGenerator()
              const structureResult = await structureGen.generateCompleteStory({
                projectId,
                action: 'generate_structure',
                options: {
                  includeCharacters: false,
                  includeWorldBuilding: true,
                  includeStoryBible: false,
                  regenerateExisting: true
                }
              })

              return NextResponse.json({
                success: true,
                data: structureResult.structure,
                message: 'Story structure regenerated successfully'
              })

            case 'characters':
              // Regenerate characters
              const charGen = new ComprehensiveStoryGenerator()
              const charResult = await charGen.generateCompleteStory({
                projectId,
                action: 'generate_characters',
                options: {
                  includeCharacters: true,
                  includeWorldBuilding: false,
                  includeStoryBible: false,
                  regenerateExisting: true
                }
              })

              return NextResponse.json({
                success: true,
                data: charResult.characters,
                message: 'Characters regenerated successfully'
              })

            case 'chapter':
              if (!chapterNumber) {
                return NextResponse.json({ error: 'Chapter number is required for chapter regeneration' }, { status: 400 })
              }

              // Use the enhanced chapter generator for single chapter
              const response = await fetch(request.url, {
                method: 'POST',
                headers: request.headers,
                body: JSON.stringify({
                  ...validationResult.data,
                  action: 'generate_chapter_enhanced'
                })
              })

              return response

            default:
              return NextResponse.json({ error: 'Invalid regenerate option' }, { status: 400 })
          }
        } catch (error) {
          logger.error('Regeneration failed:', error)
          return NextResponse.json({
            error: `Failed to regenerate ${regenerateOption}`,
            details: error instanceof Error ? error.message : String(error)
          }, { status: 500 })
        }
      }

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    logger.error('Agent generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate content' },
      { status: 500 }
    )
  }
}