-- Migration: Project invitations table
-- Dependencies: 001_enhanced_schema.sql (users, projects)
-- Rollback: DROP TABLE IF EXISTS project_invitations CASCADE;

BEGIN;

-- Create project invitations table
CREATE TABLE IF NOT EXISTS project_invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  role VARCHAR(20) NOT NULL CHECK (role IN ('editor', 'viewer')),
  token VARCHAR(255) UNIQUE NOT NULL,
  inviter_id UUID NOT NULL REFERENCES users(id),
  user_id UUID REFERENCES users(id), -- Set when invitation is accepted
  status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired')),
  expires_at TIMESTAMPTZ NOT NULL,
  accepted_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- <PERSON><PERSON> indexes
CREATE INDEX idx_project_invitations_project_id ON project_invitations(project_id);
CREATE INDEX idx_project_invitations_email ON project_invitations(email);
CREATE INDEX idx_project_invitations_token ON project_invitations(token);
CREATE INDEX idx_project_invitations_status ON project_invitations(status);
CREATE INDEX idx_project_invitations_expires_at ON project_invitations(expires_at);

-- Enable RLS
ALTER TABLE project_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Project owners and collaborators with team management can view invitations
CREATE POLICY "Users can view invitations for their projects" ON project_invitations
  FOR SELECT USING (
    project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
      UNION
      SELECT project_id FROM project_collaborators 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Anyone with a valid token can view the invitation
CREATE POLICY "Anyone can view invitation by token" ON project_invitations
  FOR SELECT USING (true);

-- Project owners can create invitations
CREATE POLICY "Project owners can create invitations" ON project_invitations
  FOR INSERT WITH CHECK (
    project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
    )
  );

-- Allow updating invitation when accepting
CREATE POLICY "Users can accept their invitations" ON project_invitations
  FOR UPDATE USING (
    token IS NOT NULL AND status = 'pending'
  );

-- Project owners can delete invitations
CREATE POLICY "Project owners can delete invitations" ON project_invitations
  FOR DELETE USING (
    project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
    )
  );

-- Function to automatically expire old invitations
CREATE OR REPLACE FUNCTION expire_old_invitations()
RETURNS void AS $$
BEGIN
  UPDATE project_invitations
  SET status = 'expired', updated_at = NOW()
  WHERE status = 'pending' AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to expire invitations (requires pg_cron extension)
-- This would be set up in Supabase dashboard or via SQL if pg_cron is available
-- SELECT cron.schedule('expire-invitations', '0 * * * *', 'SELECT expire_old_invitations();');

COMMIT;