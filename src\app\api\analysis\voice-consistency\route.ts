import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { VoiceProfileManager } from '@/lib/services/voice-profile-manager';
import { logger } from '@/lib/services/logger'

const voiceProfileManager = new VoiceProfileManager();

export async function POST(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { content, profileId, projectId, chapterId } = body;

    if (!content || !profileId) {
      return NextResponse.json(
        { error: 'Content and profileId are required' },
        { status: 400 }
      );
    }

    if (content.length < 100) {
      return NextResponse.json(
        { error: 'Content must be at least 100 characters for analysis' },
        { status: 400 }
      );
    }

    const analysis = await voiceProfileManager.analyzeVoiceConsistency(
      content,
      profileId,
      projectId,
      chapterId
    );

    return NextResponse.json({
      consistencyScore: analysis.score,
      suggestions: analysis.suggestions,
      deviations: analysis.deviations,
    });
  } catch (error) {
    logger.error('Error analyzing voice consistency:', error);
    return NextResponse.json(
      { error: 'Failed to analyze voice consistency' },
      { status: 500 }
    );
  }
}