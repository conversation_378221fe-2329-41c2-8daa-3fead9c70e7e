import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { logger } from '@/lib/services/logger'

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')

    if (!projectId) {
      return NextResponse.json({ error: 'projectId is required' }, { status: 400 })
    }

    const supabase = await createClient()

    // Get chapters with their quality metrics
    const { data: chapters, error: chaptersError } = await supabase
      .from('chapters')
      .select(`
        id,
        title,
        content,
        word_count,
        updated_at,
        quality_metrics (
          overall_score,
          created_at
        )
      `)
      .eq('project_id', projectId)
      .eq('user_id', authResult.user.id)
      .order('chapter_number', { ascending: true })

    if (chaptersError) {
      throw chaptersError
    }

    // Process chapters to include latest quality scores
    const processedChapters = chapters?.map(chapter => {
      // Get the most recent quality metric
      const latestQuality = chapter.quality_metrics?.[0]
      
      return {
        id: chapter.id,
        title: chapter.title,
        quality_score: latestQuality?.overall_score || 0,
        word_count: chapter.word_count || 0,
        last_analyzed: latestQuality?.created_at || chapter.updated_at
      }
    }) || []

    // Calculate aggregate statistics
    const totalChapters = processedChapters.length
    const analyzedChapters = processedChapters.filter(c => c.quality_score > 0).length
    const avgQualityScore = analyzedChapters > 0 
      ? Math.round(
          processedChapters
            .filter(c => c.quality_score > 0)
            .reduce((sum, c) => sum + c.quality_score, 0) / analyzedChapters
        )
      : 0

    return NextResponse.json({
      chapters: processedChapters,
      statistics: {
        totalChapters,
        analyzedChapters,
        avgQualityScore,
        analysisProgress: totalChapters > 0 ? Math.round((analyzedChapters / totalChapters) * 100) : 0
      }
    })

  } catch (error) {
    return handleRouteError(error, 'Chapter Analytics')
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const body = await request.json()
    const { chapterId, projectId } = body

    if (!chapterId || !projectId) {
      return NextResponse.json({ 
        error: 'chapterId and projectId are required' 
      }, { status: 400 })
    }

    const supabase = await createClient()

    // Get chapter content
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select('id, title, content')
      .eq('id', chapterId)
      .eq('project_id', projectId)
      .eq('user_id', authResult.user.id)
      .single()

    if (chapterError || !chapter) {
      return NextResponse.json({ error: 'Chapter not found' }, { status: 404 })
    }

    if (!chapter.content) {
      return NextResponse.json({ error: 'Chapter has no content to analyze' }, { status: 400 })
    }

    // Trigger quality analysis via the quality API
    const qualityResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/analytics/quality`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.get('Authorization') || '',
      },
      body: JSON.stringify({
        projectId,
        content: chapter.content
      })
    })

    if (!qualityResponse.ok) {
      throw new Error('Failed to analyze chapter quality')
    }

    const qualityData = await qualityResponse.json()

    // Update chapter with quality score reference
    const { error: updateError } = await supabase
      .from('chapters')
      .update({ 
        word_count: chapter.content.split(/\s+/).length,
        updated_at: new Date().toISOString()
      })
      .eq('id', chapterId)

    if (updateError) {
      logger.error('Failed to update chapter:', updateError)
    }

    return NextResponse.json({
      message: 'Chapter quality analysis completed',
      chapterId,
      qualityScore: qualityData.qualityMetrics?.overall_score || 0
    })

  } catch (error) {
    return handleRouteError(error, 'Chapter Quality Analysis')
  }
}