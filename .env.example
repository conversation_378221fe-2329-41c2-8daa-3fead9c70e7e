# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Google Gemini Configuration (Optional - used as fallback)
GOOGLE_GEMINI_API_KEY=your_google_gemini_api_key

# Stripe Payment Processing
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Email Services
MAILEROO_API_KEY=your_maileroo_api_key
ZERUH_API_KEY=your_zeruh_api_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# Collaboration Features
NEXT_PUBLIC_ENABLE_COLLABORATION=true
NEXT_PUBLIC_SUPABASE_REALTIME_URL=wss://your_project.supabase.co/realtime/v1

# Security
JWT_SECRET=your_jwt_secret_for_additional_security
SENTRY_AUTH_TOKEN=your_sentry_auth_token_for_source_maps

# Monitoring (Optional)
SENTRY_DSN=your_sentry_dsn
NEXT_PUBLIC_SENTRY_DSN=your_public_sentry_dsn

# Development Only (Set to false in production!)
NEXT_PUBLIC_DEV_BYPASS_AUTH=false
NEXT_PUBLIC_DEMO_MODE=false