import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { VersionHistoryService } from '@/lib/version-history'
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { chapterId, versionId } = body

    if (!chapterId || !versionId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Verify user owns the chapter
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select('project_id')
      .eq('id', chapterId)
      .single()

    if (chapterError || !chapter) {
      return NextResponse.json({ error: 'Chapter not found' }, { status: 404 })
    }

    // Verify user owns the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', chapter.project_id)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    // Verify user owns the version
    const { data: version, error: versionError } = await supabase
      .from('chapter_versions')
      .select('id')
      .eq('id', versionId)
      .eq('user_id', user.id)
      .single()

    if (versionError || !version) {
      return NextResponse.json({ error: 'Version not found' }, { status: 404 })
    }

    // Restore version
    const versionService = new VersionHistoryService()
    const success = await versionService.restoreChapterVersion(chapterId, versionId, user.id)

    if (!success) {
      return NextResponse.json({ error: 'Failed to restore version' }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    logger.error('Restore version error:', error)
    return NextResponse.json(
      { error: 'Failed to restore version' },
      { status: 500 }
    )
  }
}