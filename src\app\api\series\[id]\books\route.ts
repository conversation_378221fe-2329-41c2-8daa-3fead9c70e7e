import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger'

// GET - Fetch all books in a series
export async function GET(
  _request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // First, verify the series belongs to the user
    const { data: series, error: seriesError } = await supabase
      .from('series')
      .select('id, title, user_id')
      .eq('id', seriesId)
      .eq('user_id', user.id)
      .single()

    if (seriesError || !series) {
      return NextResponse.json({ error: 'Series not found' }, { status: 404 })
    }

    // Fetch books in the series
    const { data: seriesBooks, error } = await supabase
      .from('series_books')
      .select(`
        book_number,
        book_role,
        chronological_order,
        publication_order,
        introduces_characters,
        concludes_arcs,
        sets_up_future,
        projects (
          id,
          title,
          description,
          status,
          current_word_count,
          target_word_count,
          primary_genre,
          created_at,
          updated_at
        )
      `)
      .eq('series_id', seriesId)
      .order('book_number', { ascending: true })

    if (error) {
      logger.error('Error fetching series books:', error)
      return NextResponse.json({ error: 'Failed to fetch series books' }, { status: 500 })
    }

    // Transform the data to flatten the structure
    const books = seriesBooks?.map(sb => ({
      ...sb.projects,
      book_number: sb.book_number,
      book_role: sb.book_role,
      chronological_order: sb.chronological_order,
      publication_order: sb.publication_order,
      introduces_characters: sb.introduces_characters,
      concludes_arcs: sb.concludes_arcs,
      sets_up_future: sb.sets_up_future,
    })) || []

    return NextResponse.json({ 
      series: {
        id: series.id,
        title: series.title,
      },
      books 
    })
  } catch (error) {
    logger.error('Error in series books GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Add a book to a series
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { projectId, bookNumber, bookRole = 'main' } = body

    if (!projectId || !bookNumber) {
      return NextResponse.json({ error: 'projectId and bookNumber are required' }, { status: 400 })
    }

    // Verify series ownership
    const { data: series } = await supabase
      .from('series')
      .select('id')
      .eq('id', seriesId)
      .eq('user_id', user.id)
      .single()

    if (!series) {
      return NextResponse.json({ error: 'Series not found' }, { status: 404 })
    }

    // Verify project ownership
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Add book to series
    const { data: seriesBook, error } = await supabase
      .from('series_books')
      .insert({
        series_id: seriesId,
        project_id: projectId,
        book_number: bookNumber,
        book_role: bookRole,
        chronological_order: bookNumber,
        publication_order: bookNumber,
      })
      .select()
      .single()

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json({ error: 'Book already in series or book number already taken' }, { status: 400 })
      }
      logger.error('Error adding book to series:', error)
      return NextResponse.json({ error: 'Failed to add book to series' }, { status: 500 })
    }

    // Update series book count
    await supabase
      .from('series')
      .update({ current_book_count: bookNumber })
      .eq('id', seriesId)
      .gte('current_book_count', bookNumber)

    return NextResponse.json({ seriesBook }, { status: 201 })
  } catch (error) {
    logger.error('Error in series books POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}