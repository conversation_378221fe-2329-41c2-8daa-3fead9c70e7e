'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Download, FileText, FileType, Book, File } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { useAchievementTracker } from '@/hooks/use-achievement-tracker'
import { createClient } from '@/lib/supabase/client'

interface ExportButtonProps {
  projectId: string
  tier?: string
}

const exportFormats = [
  { format: 'txt', label: 'Plain Text (.txt)', icon: FileText, tiers: ['free', 'starter', 'professional', 'studio'] },
  { format: 'markdown', label: 'Markdown (.md)', icon: FileType, tiers: ['free', 'starter', 'professional', 'studio'] },
  { format: 'docx', label: 'Word Document (.docx)', icon: FileType, tiers: ['starter', 'professional', 'studio'] },
  { format: 'pdf', label: 'PDF Document', icon: File, tiers: ['professional', 'studio'] },
  { format: 'epub', label: 'EPUB (E-book)', icon: Book, tiers: ['professional', 'studio'] },
]

export function ExportButton({ projectId, tier = 'free' }: ExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false)
  const { toast } = useToast()
  const { trackExport } = useAchievementTracker()
  const supabase = createClient()

  const handleExport = async (format: string) => {
    setIsExporting(true)

    try {
      const response = await fetch(`/api/projects/${projectId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ format }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Export failed')
      }

      // Get the filename from the response headers
      const contentDisposition = response.headers.get('content-disposition')
      const filename = contentDisposition?.match(/filename="(.+)"/)?.[1] || `export.${format}`

      // Create a blob from the response
      const blob = await response.blob()

      // Create a download link
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      // Track the export achievement on client side as well
      await trackExport(format)

      toast({
        title: 'Export successful',
        description: `Your project has been exported as ${format.toUpperCase()}.`,
      })
    } catch (error) {
      toast({
        title: 'Export failed',
        description: error instanceof Error ? error.message : 'Failed to export project',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  const availableFormats = exportFormats.filter(format => format.tiers.includes(tier))

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" disabled={isExporting}>
          <Download className="mr-2 h-4 w-4" />
          {isExporting ? 'Exporting...' : 'Export'}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {availableFormats.map((format) => {
          const Icon = format.icon
          return (
            <DropdownMenuItem
              key={format.format}
              onClick={() => handleExport(format.format)}
              disabled={isExporting}
            >
              <Icon className="mr-2 h-4 w-4" />
              {format.label}
            </DropdownMenuItem>
          )
        })}
        {exportFormats.filter(f => !f.tiers.includes(tier)).map((format) => {
          const Icon = format.icon
          const requiredTier = format.tiers[0]
          return (
            <DropdownMenuItem
              key={format.format}
              disabled
              className="opacity-50"
            >
              <Icon className="mr-2 h-4 w-4" />
              {format.label}
              <span className="ml-auto text-xs text-muted-foreground">
                {requiredTier}+
              </span>
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}