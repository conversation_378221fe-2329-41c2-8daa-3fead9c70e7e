import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import Stripe from 'stripe'
import { createClient } from '@/lib/supabase/server'
import { globalLimiter } from '@/lib/rate-limiter'
import { config } from '@/lib/config'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'

const stripe = new Stripe(config.stripe.secretKey, {
  apiVersion: '2025-06-30.basil',
})

// Validation schema for payment intent
const createPaymentIntentSchema = z.object({
  amount: z.number()
    .int('Amount must be an integer')
    .min(50, 'Amount must be at least 50 cents')
    .max(999999, 'Amount exceeds maximum allowed'),
  currency: z.string()
    .regex(/^[a-z]{3}$/, 'Currency must be a 3-letter ISO code')
    .default('usd'),
  metadata: z.record(z.string())
    .optional()
    .refine(
      (meta) => {
        if (!meta) return true;
        // Ensure metadata values are not too long
        return Object.values(meta).every(val => val.length <= 500);
      },
      'Metadata values must not exceed 500 characters'
    )
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.headers.get('x-forwarded-for') ?? request.headers.get('x-real-ip') ?? 'unknown'
    try {
      await globalLimiter.check(5, ip) // 5 requests per minute
    } catch {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      )
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate request body
    const validationResult = createPaymentIntentSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }
    
    const { amount, currency, metadata } = validationResult.data

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      metadata: {
        userId: user.id,
        userEmail: user.email!,
        ...metadata
      },
      automatic_payment_methods: {
        enabled: true,
      },
    })

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    })

  } catch (error) {
    logger.error('Payment intent creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    )
  }
}