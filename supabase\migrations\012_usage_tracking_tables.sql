-- Migration: Usage tracking tables for AI and subscriptions
-- Dependencies: 001_enhanced_schema.sql (users, projects)
-- Rollback: DROP TABLE IF EXISTS ai_usage_logs, subscription_usage CASCADE;

BEGIN;

-- AI Usage Logs table
CREATE TABLE IF NOT EXISTS ai_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  model_used VARCHAR(100) NOT NULL,
  action VARCHAR(100) NOT NULL,
  words_generated INTEGER NOT NULL DEFAULT 0,
  tokens_used INTEGER NOT NULL DEFAULT 0,
  cost_estimate DECIMAL(10, 6),
  metadata JSONB,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX idx_ai_usage_user_timestamp ON ai_usage_logs(user_id, timestamp DESC);
CREATE INDEX idx_ai_usage_project ON ai_usage_logs(project_id);
CREATE INDEX idx_ai_usage_model ON ai_usage_logs(model_used);
CREATE INDEX idx_ai_usage_timestamp ON ai_usage_logs(timestamp DESC);

-- Subscription Usage table
CREATE TABLE IF NOT EXISTS subscription_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE SET NULL,
  period_start TIMESTAMPTZ NOT NULL,
  period_end TIMESTAMPTZ NOT NULL,
  ai_words_used INTEGER NOT NULL DEFAULT 0,
  ai_words_limit INTEGER NOT NULL,
  projects_count INTEGER NOT NULL DEFAULT 0,
  projects_limit INTEGER NOT NULL,
  collaborators_count INTEGER NOT NULL DEFAULT 0,
  collaborators_limit INTEGER NOT NULL,
  export_count JSONB DEFAULT '{"pdf": 0, "epub": 0, "docx": 0}'::jsonb,
  export_limits JSONB DEFAULT '{"pdf": 0, "epub": 0, "docx": -1}'::jsonb,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_subscription_usage_user ON subscription_usage(user_id);
CREATE INDEX idx_subscription_usage_period ON subscription_usage(user_id, period_start, period_end);
CREATE UNIQUE INDEX idx_subscription_usage_current ON subscription_usage(user_id, period_end) 
  WHERE period_end > NOW();

-- Enable RLS
ALTER TABLE ai_usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_usage ENABLE ROW LEVEL SECURITY;

-- RLS Policies for ai_usage_logs
CREATE POLICY "Users can view their own AI usage" ON ai_usage_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert AI usage logs" ON ai_usage_logs
  FOR INSERT WITH CHECK (true);

-- RLS Policies for subscription_usage
CREATE POLICY "Users can view their own subscription usage" ON subscription_usage
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage subscription usage" ON subscription_usage
  FOR ALL USING (true);

-- Function to get current usage for a user
CREATE OR REPLACE FUNCTION get_current_usage(p_user_id UUID)
RETURNS TABLE (
  ai_words_used INTEGER,
  ai_words_limit INTEGER,
  ai_words_percentage DECIMAL,
  projects_count INTEGER,
  projects_limit INTEGER,
  collaborators_count INTEGER,
  collaborators_limit INTEGER,
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    su.ai_words_used,
    su.ai_words_limit,
    CASE 
      WHEN su.ai_words_limit = -1 THEN 0::DECIMAL
      WHEN su.ai_words_limit = 0 THEN 100::DECIMAL
      ELSE ROUND((su.ai_words_used::DECIMAL / su.ai_words_limit) * 100, 2)
    END as ai_words_percentage,
    su.projects_count,
    su.projects_limit,
    su.collaborators_count,
    su.collaborators_limit,
  FROM subscription_usage su
  WHERE su.user_id = p_user_id
    AND su.period_end > NOW()
  ORDER BY su.created_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to track AI usage
CREATE OR REPLACE FUNCTION track_ai_usage(
  p_user_id UUID,
  p_project_id UUID,
  p_model VARCHAR,
  p_action VARCHAR,
  p_words INTEGER,
  p_tokens INTEGER DEFAULT 0
)
RETURNS VOID AS $$
DECLARE
  v_cost DECIMAL;
BEGIN
  -- Calculate estimated cost based on model
  v_cost := CASE p_model
    WHEN 'gpt-4.1' THEN (p_tokens / 1000000.0) * 2.0  -- $2 per 1M tokens
    WHEN 'gpt-4.1-mini' THEN (p_tokens / 1000000.0) * 0.4
    WHEN 'gpt-4o-mini' THEN (p_tokens / 1000000.0) * 0.15
    WHEN 'text-embedding-3-small' THEN (p_tokens / 1000000.0) * 0.02
    ELSE 0
  END;

  -- Insert usage log
  INSERT INTO ai_usage_logs (
    user_id, project_id, model_used, action, 
    words_generated, tokens_used, cost_estimate
  ) VALUES (
    p_user_id, p_project_id, p_model, p_action,
    p_words, p_tokens, v_cost
  );

  -- Update subscription usage
  UPDATE subscription_usage
  SET 
    ai_words_used = ai_words_used + p_words,
    updated_at = NOW()
  WHERE user_id = p_user_id
    AND period_end > NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to check if user has exceeded limits
CREATE OR REPLACE FUNCTION check_usage_limits(p_user_id UUID, p_type VARCHAR)
RETURNS BOOLEAN AS $$
DECLARE
  v_usage subscription_usage%ROWTYPE;
BEGIN
  SELECT * INTO v_usage
  FROM subscription_usage
  WHERE user_id = p_user_id
    AND period_end > NOW()
  LIMIT 1;

  IF NOT FOUND THEN
    RETURN FALSE; -- No active subscription
  END IF;

  CASE p_type
    WHEN 'ai_words' THEN
      RETURN v_usage.ai_words_limit = -1 OR v_usage.ai_words_used < v_usage.ai_words_limit;
    WHEN 'projects' THEN
      RETURN v_usage.projects_limit = -1 OR v_usage.projects_count < v_usage.projects_limit;
    WHEN 'collaborators' THEN
      RETURN v_usage.collaborators_limit = -1 OR v_usage.collaborators_count < v_usage.collaborators_limit;
    ELSE
      RETURN FALSE;
  END CASE;
END;
$$ LANGUAGE plpgsql;

COMMIT;