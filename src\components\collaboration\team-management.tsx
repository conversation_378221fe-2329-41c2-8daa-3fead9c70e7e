'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import {
  Users,
  UserPlus,
  Mail,
  Shield,
  Clock,
  Edit,
  Eye,
  Settings,
  Trash2,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Copy,
  Loader2,
  Crown,
  PenTool,
  BookOpen
} from 'lucide-react'

interface TeamMember {
  id: string
  email: string
  name?: string
  avatar_url?: string
  role: 'owner' | 'editor' | 'viewer'
  status: 'active' | 'pending' | 'suspended'
  joined_at?: string
  last_active?: string
  permissions: {
    can_write: boolean
    can_manage_team: boolean
    can_export: boolean
    can_delete: boolean
  }
}

interface ProjectCollaborator {
  user_id: string
  project_id: string
  role: string
  created_at: string
}

interface TeamManagementProps {
  projectId: string
  projectTitle: string
  userId: string
  maxCollaborators: number
}

export function TeamManagement({ 
  projectId, 
  projectTitle,
  userId,
  maxCollaborators = 3 
}: TeamManagementProps) {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [pendingInvites, setPendingInvites] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isInviting, setIsInviting] = useState(false)
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null)
  const { toast } = useToast()

  // Invite form state
  const [inviteEmail, setInviteEmail] = useState('')
  const [inviteRole, setInviteRole] = useState<'editor' | 'viewer'>('viewer')
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false)

  useEffect(() => {
    loadTeamMembers()
  }, [projectId])

  const loadTeamMembers = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/projects/${projectId}/team`)
      if (!response.ok) throw new Error('Failed to load team members')
      
      const data = await response.json()
      setTeamMembers(data.members || [])
      setPendingInvites(data.pendingInvites || [])
    } catch (error) {
      logger.error('Error loading team members:', error)
      toast({
        title: "Error",
        description: "Failed to load team members",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const inviteTeamMember = async () => {
    if (!inviteEmail || !inviteEmail.includes('@')) {
      toast({
        title: "Error",
        description: "Please enter a valid email address",
        variant: "destructive"
      })
      return
    }

    if (teamMembers.length >= maxCollaborators) {
      toast({
        title: "Team Limit Reached",
        description: `You can only have ${maxCollaborators} team members on your current plan`,
        variant: "destructive"
      })
      return
    }

    setIsInviting(true)
    try {
      const response = await fetch('/api/projects/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          email: inviteEmail,
          role: inviteRole,
        }),
      })
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to send invitation')
      }
      
      const data = await response.json()
      
      // Add to pending invites
      setPendingInvites([...pendingInvites, inviteEmail])
      
      toast({
        title: "Invitation Sent",
        description: `An invitation has been sent to ${inviteEmail}`,
      })
      
      setInviteEmail('')
      setInviteRole('viewer')
      setInviteDialogOpen(false)
      
      // Reload team members
      loadTeamMembers()
    } catch (error) {
      logger.error('Error inviting team member:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send invitation",
        variant: "destructive"
      })
    } finally {
      setIsInviting(false)
    }
  }

  const updateMemberRole = async (memberId: string, newRole: 'editor' | 'viewer') => {
    try {
      // In production, this would update via API
      setTeamMembers(members => 
        members.map(m => 
          m.id === memberId 
            ? { 
                ...m, 
                role: newRole,
                permissions: getRolePermissions(newRole)
              }
            : m
        )
      )
      
      toast({
        title: "Role Updated",
        description: "Team member role has been updated",
      })
    } catch (error) {
      logger.error('Error updating member role:', error)
      toast({
        title: "Error",
        description: "Failed to update member role",
        variant: "destructive"
      })
    }
  }

  const removeMember = async (memberId: string) => {
    if (!confirm('Are you sure you want to remove this team member?')) {
      return
    }

    try {
      // In production, this would remove via API
      setTeamMembers(members => members.filter(m => m.id !== memberId))
      
      toast({
        title: "Member Removed",
        description: "Team member has been removed from the project",
      })
    } catch (error) {
      logger.error('Error removing member:', error)
      toast({
        title: "Error",
        description: "Failed to remove team member",
        variant: "destructive"
      })
    }
  }

  const getRolePermissions = (role: string): TeamMember['permissions'] => {
    switch (role) {
      case 'owner':
        return {
          can_write: true,
          can_manage_team: true,
          can_export: true,
          can_delete: true
        }
      case 'editor':
        return {
          can_write: true,
          can_manage_team: false,
          can_export: true,
          can_delete: false
        }
      case 'viewer':
        return {
          can_write: false,
          can_manage_team: false,
          can_export: true,
          can_delete: false
        }
      default:
        return {
          can_write: false,
          can_manage_team: false,
          can_export: false,
          can_delete: false
        }
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return <Crown className="h-4 w-4" />
      case 'editor': return <PenTool className="h-4 w-4" />
      case 'viewer': return <Eye className="h-4 w-4" />
      default: return <Users className="h-4 w-4" />
    }
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'owner': return 'default'
      case 'editor': return 'secondary'
      case 'viewer': return 'outline'
      default: return 'outline'
    }
  }

  const getInitials = (email: string, name?: string) => {
    if (name) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase()
    }
    return email.substring(0, 2).toUpperCase()
  }

  const copyInviteLink = async () => {
    const inviteLink = `${window.location.origin}/join/${projectId}`
    await navigator.clipboard.writeText(inviteLink)
    toast({
      title: "Link Copied",
      description: "Project invite link copied to clipboard",
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Team Collaboration</h2>
          <p className="text-sm text-muted-foreground">
            Manage who can access and edit "{projectTitle}"
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {teamMembers.length} / {maxCollaborators} members
          </Badge>
          
          <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
            <DialogTrigger asChild>
              <Button 
                disabled={teamMembers.length >= maxCollaborators}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Invite Member
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Invite Team Member</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 mt-4">
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <Label htmlFor="role">Role</Label>
                  <Select value={inviteRole} onValueChange={(value) => setInviteRole(value as 'editor' | 'viewer')}>
                    <SelectTrigger id="role">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="editor">
                        <div className="flex items-center gap-2">
                          <PenTool className="h-4 w-4" />
                          Editor - Can write and edit
                        </div>
                      </SelectItem>
                      <SelectItem value="viewer">
                        <div className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          Viewer - Read-only access
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <Alert>
                  <Mail className="h-4 w-4" />
                  <AlertDescription>
                    An email invitation will be sent with a link to join this project.
                  </AlertDescription>
                </Alert>
                
                <div className="flex gap-2">
                  <Button 
                    onClick={inviteTeamMember} 
                    disabled={isInviting || !inviteEmail}
                  >
                    {isInviting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      'Send Invitation'
                    )}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setInviteDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Team Members */}
      <Card>
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
          <CardDescription>
            People who have access to this project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            <div className="space-y-4">
              {teamMembers.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    <Avatar>
                      <AvatarImage src={member.avatar_url} />
                      <AvatarFallback>
                        {getInitials(member.email, member.name)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div>
                      <div className="flex items-center gap-2">
                        <p className="font-medium">
                          {member.name || member.email}
                        </p>
                        <Badge variant={getRoleBadgeVariant(member.role)}>
                          {getRoleIcon(member.role)}
                          <span className="ml-1">{member.role}</span>
                        </Badge>
                        {member.status === 'pending' && (
                          <Badge variant="outline" className="text-yellow-600">
                            <Clock className="h-3 w-3 mr-1" />
                            Pending
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {member.email}
                      </p>
                      {member.last_active && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Last active: {new Date(member.last_active).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {member.role !== 'owner' && (
                      <>
                        <Select
                          value={member.role}
                          onValueChange={(value) => updateMemberRole(member.id, value as 'editor' | 'viewer')}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="editor">Editor</SelectItem>
                            <SelectItem value="viewer">Viewer</SelectItem>
                          </SelectContent>
                        </Select>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeMember(member.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              ))}
              
              {pendingInvites.length > 0 && (
                <>
                  <div className="text-sm font-medium text-muted-foreground mt-6 mb-2">
                    Pending Invitations
                  </div>
                  {pendingInvites.map((email, idx) => (
                    <div
                      key={idx}
                      className="flex items-center justify-between p-4 border rounded-lg opacity-60"
                    >
                      <div className="flex items-center gap-4">
                        <Avatar>
                          <AvatarFallback>
                            {email.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{email}</p>
                          <p className="text-sm text-muted-foreground">
                            Invitation sent
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-yellow-600">
                        <Clock className="h-3 w-3 mr-1" />
                        Pending
                      </Badge>
                    </div>
                  ))}
                </>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Permissions Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Permissions Overview</CardTitle>
          <CardDescription>
            What each role can do in this project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-4 gap-4 text-sm font-medium">
              <div>Permission</div>
              <div className="text-center">Owner</div>
              <div className="text-center">Editor</div>
              <div className="text-center">Viewer</div>
            </div>
            
            {[
              { name: 'View content', owner: true, editor: true, viewer: true },
              { name: 'Edit content', owner: true, editor: true, viewer: false },
              { name: 'Manage AI agents', owner: true, editor: true, viewer: false },
              { name: 'Export project', owner: true, editor: true, viewer: true },
              { name: 'Manage team', owner: true, editor: false, viewer: false },
              { name: 'Delete project', owner: true, editor: false, viewer: false },
              { name: 'Change settings', owner: true, editor: false, viewer: false },
            ].map((permission, idx) => (
              <div key={idx} className="grid grid-cols-4 gap-4 text-sm py-2 border-t">
                <div>{permission.name}</div>
                <div className="text-center">
                  {permission.owner ? (
                    <CheckCircle2 className="h-4 w-4 text-green-500 mx-auto" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500 mx-auto" />
                  )}
                </div>
                <div className="text-center">
                  {permission.editor ? (
                    <CheckCircle2 className="h-4 w-4 text-green-500 mx-auto" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500 mx-auto" />
                  )}
                </div>
                <div className="text-center">
                  {permission.viewer ? (
                    <CheckCircle2 className="h-4 w-4 text-green-500 mx-auto" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500 mx-auto" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Sharing Options */}
      <Card>
        <CardHeader>
          <CardTitle>Sharing Options</CardTitle>
          <CardDescription>
            Additional ways to share this project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">Share via Link</p>
                <p className="text-sm text-muted-foreground">
                  Anyone with this link can request access
                </p>
              </div>
              <Button variant="outline" onClick={copyInviteLink}>
                <Copy className="h-4 w-4 mr-2" />
                Copy Link
              </Button>
            </div>
            
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                All team members must have a BookScribe account to access shared projects.
                Invitations expire after 7 days if not accepted.
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}