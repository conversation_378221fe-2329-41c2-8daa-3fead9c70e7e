import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { authenticateUser } from '@/lib/auth/server';
import { globalLimiter } from '@/lib/rate-limiter';
import { createClient } from '@/lib/supabase/server';
import { stripe, STRIPE_PRICES } from '@/lib/stripe';
import { config } from '@/lib/config';
import type Stripe from 'stripe';
import type { SubscriptionStatus } from '@/lib/db/types/enums';
import { logger } from '@/lib/services/logger'

// Extended Stripe subscription type with period properties
interface StripeSubscriptionWithPeriods extends Stripe.Subscription {
  current_period_start: number;
  current_period_end: number;
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.headers.get('x-forwarded-for') ?? request.headers.get('x-real-ip') ?? 'unknown'
    try {
      await globalLimiter.check(5, ip) // 5 requests per minute
    } catch {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      )
    }

    // Authentication
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const supabase = await createClient()
    const user = authResult.user

    // Parse the request body
    const body = await request.json();
    const { paymentMethodId, tierId, type = 'subscription' } = body;
    
    // Validate required fields
    if (!paymentMethodId || !tierId) {
      return NextResponse.json(
        { error: 'Missing required fields: paymentMethodId or tierId' },
        { status: 400 }
      );
    }
    
    // Validate tier ID is one of our known prices
    const validPriceIds = Object.values(STRIPE_PRICES);
    if (!validPriceIds.includes(tierId)) {
      return NextResponse.json(
        { error: 'Invalid tier ID' },
        { status: 400 }
      );
    }

    // Get or create customer
    let customer: Stripe.Customer;
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single()

    if (profile?.stripe_customer_id) {
      customer = await stripe.customers.retrieve(profile.stripe_customer_id) as Stripe.Customer;
    } else {
      customer = await stripe.customers.create({
        email: user.email!,
        metadata: { userId: user.id }
      });

      // Save customer ID to profile
      await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          email: user.email!,
          stripe_customer_id: customer.id
        })
    }

    // Attach payment method to customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: customer.id,
    });

    if (type === 'subscription') {
      // Create subscription
      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: tierId }],
        default_payment_method: paymentMethodId,
        metadata: {
          userId: user.id,
          tierId
        }
      });

      // Save subscription to database
      // Cast to unknown first, then to our extended type to access period properties
      const stripeSubscription = subscription as unknown as StripeSubscriptionWithPeriods;
      await supabase.from('user_subscriptions').insert({
        user_id: user.id,
        tier_id: tierId,
        status: stripeSubscription.status as SubscriptionStatus,
        stripe_subscription_id: stripeSubscription.id,
        stripe_customer_id: customer.id,
        current_period_start: new Date(stripeSubscription.current_period_start * 1000),
        current_period_end: new Date(stripeSubscription.current_period_end * 1000),
        cancel_at_period_end: stripeSubscription.cancel_at_period_end
      });

      return NextResponse.json({
        subscriptionId: subscription.id,
        status: subscription.status,
        clientSecret: subscription.latest_invoice
      });
    } else {
      // One-time payment
      const paymentIntent = await stripe.paymentIntents.create({
        amount: body.amount || 1000, // Default $10
        currency: body.currency || 'usd',
        customer: customer.id,
        payment_method: paymentMethodId,
        confirmation_method: 'manual',
        confirm: true,
        metadata: {
          userId: user.id,
          type: 'one_time_payment'
        }
      });

      return NextResponse.json({
        paymentIntentId: paymentIntent.id,
        status: paymentIntent.status,
        clientSecret: paymentIntent.client_secret
      });
    }
  } catch (error) {
    logger.error('Stripe charge error:', error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 400 }
    );
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': config.app.url,
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}