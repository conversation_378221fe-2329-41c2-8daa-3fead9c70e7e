import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';
import { logger } from '@/lib/services/logger'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const featured = searchParams.get('featured') === 'true';
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    let query = supabase
      .from('selection_profiles')
      .select('*')
      .eq('is_public', true)
      .order('usage_count', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    if (featured) {
      query = query.eq('is_featured', true);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%, description.ilike.%${search}%`);
    }

    const { data: profiles, error, count } = await query;

    if (error) throw error;

    // Transform data to match frontend interface
    const formattedProfiles = profiles?.map(profile => ({
      id: profile.id,
      name: profile.name,
      description: profile.description,
      category: profile.category,
      isPublic: profile.is_public,
      isFeatured: profile.is_featured,
      settings: profile.settings,
      usageCount: profile.usage_count,
      tags: profile.tags || [],
      createdAt: new Date(profile.created_at),
      createdBy: 'Community' // Public profiles don't show individual creators
    })) || [];

    // Get categories for filter options
    const { data: categories } = await supabase
      .from('selection_profiles')
      .select('category')
      .eq('is_public', true)
      .not('category', 'is', null);

    const uniqueCategories = [...new Set(categories?.map(p => p.category) || [])];

    return NextResponse.json({ 
      profiles: formattedProfiles,
      categories: uniqueCategories,
      pagination: {
        offset,
        limit,
        total: count || 0,
        hasMore: (count || 0) > offset + limit
      }
    });
  } catch (error) {
    logger.error('Public profiles API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}