import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals'
import { NextRequest } from 'next/server'
import { POST } from '../check/route'

// Mock dependencies
jest.mock('@/lib/api/auth-middleware')
jest.mock('@/lib/supabase/server')
jest.mock('@/lib/services/logger')

const mockSupabase = {
  rpc: jest.fn(),
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  gte: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  single: jest.fn(),
  channel: jest.fn().mockReturnThis(),
  send: jest.fn()
}

const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>'
}

describe('Achievement Check API', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock successful auth
    const { requireAuth } = require('@/lib/api/auth-middleware')
    requireAuth.mockResolvedValue({ user: mockUser })
    
    // Mock Supabase client
    const { createClient } = require('@/lib/supabase/server')
    createClient.mockResolvedValue(mockSupabase)
  })

  afterEach(() => {
    jest.resetAllMocks()
  })

  it('should check achievements and return newly unlocked ones', async () => {
    // Mock achievement checking function
    const newlyUnlockedIds = ['achievement-1', 'achievement-2']
    mockSupabase.rpc.mockResolvedValueOnce({
      data: [{ newly_unlocked: newlyUnlockedIds }],
      error: null
    })

    // Mock achievement details fetch
    const mockAchievements = [
      {
        id: 'achievement-1',
        code: 'first_words',
        name: 'First Words',
        description: 'Write your first 100 words',
        points: 10,
        tier: 'bronze',
        category: 'writing'
      },
      {
        id: 'achievement-2',
        code: 'first_chapter',
        name: 'Chapter One',
        description: 'Complete your first chapter',
        points: 20,
        tier: 'bronze',
        category: 'writing'
      }
    ]

    mockSupabase.from.mockImplementation((table) => {
      if (table === 'achievements') {
        return {
          select: () => ({
            in: () => Promise.resolve({ data: mockAchievements, error: null })
          })
        }
      }
      return mockSupabase
    })

    const request = new NextRequest('http://localhost:3000/api/achievements/check', {
      method: 'POST',
      body: JSON.stringify({})
    })

    const response = await POST(request)
    const responseData = await response.json()

    expect(response.status).toBe(200)
    expect(responseData.newAchievements).toHaveLength(2)
    expect(responseData.newAchievements[0].name).toBe('First Words')
    expect(responseData.checked).toBe(true)

    // Verify achievement checking was called
    expect(mockSupabase.rpc).toHaveBeenCalledWith(
      'check_and_unlock_achievements',
      { p_user_id: mockUser.id }
    )
  })

  it('should handle achievements unlocked since last check', async () => {
    // Mock no newly unlocked achievements
    mockSupabase.rpc.mockResolvedValueOnce({
      data: [{ newly_unlocked: [] }],
      error: null
    })

    // Mock recent achievements
    const recentAchievements = [{
      achievement: {
        id: 'recent-achievement',
        name: 'Recent Achievement',
        description: 'Recently unlocked',
        points: 15
      }
    }]

    let callCount = 0
    mockSupabase.from.mockImplementation((table) => {
      callCount++
      if (table === 'achievements' && callCount === 1) {
        return {
          select: () => ({
            in: () => Promise.resolve({ data: [], error: null })
          })
        }
      } else if (table === 'user_achievements') {
        return {
          select: () => ({
            eq: () => ({
              gte: () => ({
                order: () => Promise.resolve({ data: recentAchievements, error: null })
              })
            })
          })
        }
      }
      return mockSupabase
    })

    const lastCheck = new Date(Date.now() - 60000).toISOString() // 1 minute ago
    const request = new NextRequest('http://localhost:3000/api/achievements/check', {
      method: 'POST',
      body: JSON.stringify({ lastCheck })
    })

    const response = await POST(request)
    const responseData = await response.json()

    expect(response.status).toBe(200)
    expect(responseData.newAchievements).toHaveLength(1)
    expect(responseData.newAchievements[0].name).toBe('Recent Achievement')
  })

  it('should handle authentication errors', async () => {
    const { requireAuth } = require('@/lib/api/auth-middleware')
    const authError = new Response('Unauthorized', { status: 401 })
    requireAuth.mockResolvedValue(authError)

    const request = new NextRequest('http://localhost:3000/api/achievements/check', {
      method: 'POST',
      body: JSON.stringify({})
    })

    const response = await POST(request)

    expect(response.status).toBe(401)
  })

  it('should handle database errors gracefully', async () => {
    mockSupabase.rpc.mockResolvedValueOnce({
      data: null,
      error: new Error('Database connection failed')
    })

    const request = new NextRequest('http://localhost:3000/api/achievements/check', {
      method: 'POST',
      body: JSON.stringify({})
    })

    const response = await POST(request)
    const responseData = await response.json()

    expect(response.status).toBe(500)
    expect(responseData.error).toBe('Failed to check achievements')
  })

  it('should broadcast achievement unlocks to real-time channel', async () => {
    const newlyUnlockedIds = ['achievement-1']
    mockSupabase.rpc.mockResolvedValueOnce({
      data: [{ newly_unlocked: newlyUnlockedIds }],
      error: null
    })

    const mockAchievement = {
      id: 'achievement-1',
      name: 'Test Achievement',
      description: 'Test description'
    }

    mockSupabase.from.mockImplementation(() => ({
      select: () => ({
        in: () => Promise.resolve({ data: [mockAchievement], error: null })
      })
    }))

    const request = new NextRequest('http://localhost:3000/api/achievements/check', {
      method: 'POST',
      body: JSON.stringify({})
    })

    await POST(request)

    // Verify real-time broadcast was sent
    expect(mockSupabase.channel).toHaveBeenCalledWith('achievement-updates')
    expect(mockSupabase.send).toHaveBeenCalledWith({
      type: 'broadcast',
      event: 'new-achievement',
      payload: {
        userId: mockUser.id,
        achievement: mockAchievement
      }
    })
  })
})