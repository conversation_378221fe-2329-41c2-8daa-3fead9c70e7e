import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { supabase } from '@/lib/supabase/client';
import { logger } from '@/lib/services/logger'

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;
    const supabaseClient = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { voiceProfileId, applyTo } = body;

    if (!voiceProfileId || !applyTo) {
      return NextResponse.json(
        { error: 'Voice profile ID and applyTo scope are required' },
        { status: 400 }
      );
    }

    // Verify user owns this series
    const { data: series, error: seriesError } = await supabase
      .from('series')
      .select('user_id')
      .eq('id', seriesId)
      .single();

    if (seriesError || !series || series.user_id !== user.id) {
      return NextResponse.json({ error: 'Series not found or unauthorized' }, { status: 404 });
    }

    // Verify user owns the voice profile
    const { data: voiceProfile, error: profileError } = await supabase
      .from('voice_profiles')
      .select('user_id, type')
      .eq('id', voiceProfileId)
      .single();

    if (profileError || !voiceProfile || voiceProfile.user_id !== user.id) {
      return NextResponse.json({ error: 'Voice profile not found or unauthorized' }, { status: 404 });
    }

    let updatedCount = 0;

    switch (applyTo) {
      case 'series': {
        // Apply to the series itself
        const { error: updateError } = await supabase
          .from('series')
          .update({ voice_profile_id: voiceProfileId })
          .eq('id', seriesId);

        if (updateError) throw updateError;
        updatedCount = 1;
        break;
      }

      case 'all_books': {
        // Get all books in the series
        const { data: seriesBooks, error: booksError } = await supabase
          .from('series_books')
          .select('project_id')
          .eq('series_id', seriesId);

        if (booksError) throw booksError;

        // Update all projects with the voice profile
        if (seriesBooks && seriesBooks.length > 0) {
          const projectIds = seriesBooks.map(sb => sb.project_id);
          
          const { error: updateError } = await supabase
            .from('projects')
            .update({ voice_profile_id: voiceProfileId })
            .in('id', projectIds);

          if (updateError) throw updateError;
          updatedCount = projectIds.length;
        }
        break;
      }

      case 'future_books': {
        // Just apply to the series for future books
        const { error: updateError } = await supabase
          .from('series')
          .update({ voice_profile_id: voiceProfileId })
          .eq('id', seriesId);

        if (updateError) throw updateError;
        updatedCount = 1;
        break;
      }

      case 'specific_character': {
        const { characterName } = body;
        if (!characterName) {
          return NextResponse.json(
            { error: 'Character name is required for character-specific application' },
            { status: 400 }
          );
        }

        // Update character continuity with voice profile
        const { data: continuity, error: continuityError } = await supabase
          .from('series_character_continuity')
          .upsert({
            series_id: seriesId,
            character_name: characterName,
            voice_profile_id: voiceProfileId,
            first_appearance_book: 1,
            status: 'active'
          }, {
            onConflict: 'series_id,character_name'
          })
          .select();

        if (continuityError) throw continuityError;
        updatedCount = 1;
        break;
      }

      default:
        return NextResponse.json(
          { error: 'Invalid applyTo scope' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      updatedCount,
      message: `Voice profile applied to ${updatedCount} item(s)`
    });
  } catch (error) {
    logger.error('Error applying voice profile:', error);
    return NextResponse.json(
      { error: 'Failed to apply voice profile' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;
    const supabaseClient = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get series voice profile
    const { data: series, error: seriesError } = await supabase
      .from('series')
      .select(`
        voice_profile_id,
        voice_profile:voice_profiles(
          id,
          name,
          type,
          confidence
        )
      `)
      .eq('id', seriesId)
      .eq('user_id', user.id)
      .single();

    if (seriesError) {
      return NextResponse.json({ error: 'Series not found' }, { status: 404 });
    }

    // Get all book voice profiles in the series
    const { data: bookProfiles, error: booksError } = await supabase
      .from('series_books')
      .select(`
        book_number,
        project:projects!series_books_project_id_fkey(
          id,
          title,
          voice_profile_id,
          voice_profile:voice_profiles(
            id,
            name,
            type,
            confidence
          )
        )
      `)
      .eq('series_id', seriesId)
      .order('book_number');

    if (booksError) throw booksError;

    // Get character voice profiles
    const { data: characterProfiles, error: charactersError } = await supabase
      .from('series_character_continuity')
      .select(`
        character_name,
        voice_profile_id,
        voice_profile:voice_profiles(
          id,
          name,
          type,
          confidence
        )
      `)
      .eq('series_id', seriesId)
      .not('voice_profile_id', 'is', null);

    if (charactersError) throw charactersError;

    return NextResponse.json({
      seriesProfile: series.voice_profile,
      bookProfiles: bookProfiles?.map(bp => ({
        bookNumber: bp.book_number,
        bookTitle: bp.project?.title,
        voiceProfile: bp.project?.voice_profile
      })) || [],
      characterProfiles: characterProfiles || []
    });
  } catch (error) {
    logger.error('Error fetching voice profiles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch voice profiles' },
      { status: 500 }
    );
  }
}