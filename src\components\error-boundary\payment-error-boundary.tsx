'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { CreditCard, RefreshCw, Shield, AlertTriangle, HelpCircle } from 'lucide-react'
import { logger } from '@/lib/services/logger'

interface Props {
  children: React.ReactNode
  onRetry?: () => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  isCardError: boolean
  isNetworkError: boolean
  isValidationError: boolean
}

export class PaymentErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      isCardError: false,
      isNetworkError: false,
      isValidationError: false
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorMessage = error.message.toLowerCase()
    
    return { 
      hasError: true, 
      error,
      isCardError: errorMessage.includes('card') || errorMessage.includes('payment method') || errorMessage.includes('declined'),
      isNetworkError: errorMessage.includes('network') || errorMessage.includes('stripe'),
      isValidationError: errorMessage.includes('invalid') || errorMessage.includes('required')
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Don't log sensitive payment information
    logger.error('Payment error boundary caught error', {
      error: error.message.replace(/\b\d{4,}\b/g, '****'), // Mask potential card numbers
      type: this.state.isCardError ? 'card' : this.state.isNetworkError ? 'network' : 'other'
    })

    this.setState({
      errorInfo
    })
  }

  handleReset = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      isCardError: false,
      isNetworkError: false,
      isValidationError: false
    })
  }

  handleRetry = () => {
    this.handleReset()
    this.props.onRetry?.()
  }

  render() {
    if (this.state.hasError) {
      const { isCardError, isNetworkError, isValidationError } = this.state

      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <Card className="max-w-lg w-full">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-500/10 flex items-center justify-center">
                {isCardError ? (
                  <CreditCard className="h-6 w-6 text-red-500" />
                ) : isNetworkError ? (
                  <AlertTriangle className="h-6 w-6 text-red-500" />
                ) : (
                  <Shield className="h-6 w-6 text-red-500" />
                )}
              </div>
              <CardTitle>
                {isCardError 
                  ? "Payment Failed"
                  : isNetworkError
                  ? "Connection Error"
                  : isValidationError
                  ? "Invalid Information"
                  : "Payment Error"}
              </CardTitle>
              <CardDescription>
                {isCardError 
                  ? "There was an issue processing your payment."
                  : isNetworkError
                  ? "We couldn't connect to our payment processor."
                  : isValidationError
                  ? "Please check your payment information."
                  : "Something went wrong during checkout."}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isCardError && (
                <Alert>
                  <AlertTitle>Common Issues</AlertTitle>
                  <AlertDescription>
                    <ul className="list-disc list-inside space-y-1 mt-2">
                      <li>Insufficient funds</li>
                      <li>Card expired or invalid</li>
                      <li>Billing address mismatch</li>
                      <li>Card issuer declined the charge</li>
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {isNetworkError && (
                <Alert>
                  <AlertDescription>
                    Please check your internet connection and try again. If the problem persists, 
                    our payment processor may be experiencing issues.
                  </AlertDescription>
                </Alert>
              )}

              {isValidationError && (
                <Alert>
                  <AlertDescription>
                    Make sure all required fields are filled out correctly. 
                    Check that your card number, expiration date, and CVC are valid.
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Button 
                  onClick={this.handleRetry}
                  className="w-full"
                  variant="default"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>

                <Button 
                  onClick={() => window.open('/support', '_blank')}
                  className="w-full"
                  variant="outline"
                >
                  <HelpCircle className="h-4 w-4 mr-2" />
                  Contact Support
                </Button>
              </div>

              <div className="pt-4 border-t">
                <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                  <Shield className="h-4 w-4" />
                  <span>Your payment information is secure and encrypted</span>
                </div>
              </div>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-4">
                  <summary className="text-sm text-muted-foreground cursor-pointer">
                    Error Details (Development Only)
                  </summary>
                  <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                    {this.state.error.message}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}