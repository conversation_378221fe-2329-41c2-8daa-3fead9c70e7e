#!/usr/bin/env node
/**
 * <PERSON>ript to remove console.log statements and replace with logger service
 * Run with: npx tsx scripts/remove-console-logs.ts
 */

import fs from 'fs'
import path from 'path'
import { glob } from 'glob'

const SRC_DIR = path.join(process.cwd(), 'src')

// Patterns to replace
const REPLACEMENTS = [
  {
    pattern: /console\.log\(/g,
    replacement: 'logger.info(',
    needsImport: true
  },
  {
    pattern: /console\.error\(/g,
    replacement: 'logger.error(',
    needsImport: true
  },
  {
    pattern: /console\.warn\(/g,
    replacement: 'logger.warn(',
    needsImport: true
  },
  {
    pattern: /console\.debug\(/g,
    replacement: 'logger.debug(',
    needsImport: true
  }
]

// Files to skip
const SKIP_PATTERNS = [
  '**/node_modules/**',
  '**/*.test.*',
  '**/*.spec.*',
  '**/logger.ts', // Don't modify the logger itself
  '**/scripts/**' // Don't modify scripts
]

/**
 * Check if file already imports logger
 */
function hasLoggerImport(content: string): boolean {
  return content.includes("from '@/lib/services/logger'") ||
         content.includes('from "@/lib/services/logger"') ||
         content.includes("from '../lib/services/logger'") ||
         content.includes("from '../../lib/services/logger'")
}

/**
 * Add logger import to file
 */
function addLoggerImport(content: string, filePath: string): string {
  // Skip if already has logger import
  if (hasLoggerImport(content)) {
    return content
  }
  
  // Calculate relative path to logger
  const fileDir = path.dirname(filePath)
  const loggerPath = path.join(SRC_DIR, 'lib/services/logger.ts')
  let relativePath = path.relative(fileDir, loggerPath).replace(/\\/g, '/')
  
  // Remove .ts extension
  relativePath = relativePath.replace('.ts', '')
  
  // Use @ alias if in src directory
  if (filePath.includes('/src/')) {
    relativePath = '@/lib/services/logger'
  }
  
  // Find the best place to add import
  const lines = content.split('\n')
  let importIndex = 0
  
  // Find last import statement
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes('import ')) {
      importIndex = i + 1
    } else if (lines[i].trim() !== '' && !lines[i].startsWith('//')) {
      // Stop at first non-import, non-comment, non-empty line
      if (importIndex === 0) importIndex = i
      break
    }
  }
  
  // Add import
  lines.splice(importIndex, 0, `import { logger } from '${relativePath}'`)
  
  return lines.join('\n')
}

/**
 * Process a single file
 */
async function processFile(filePath: string): Promise<boolean> {
  try {
    let content = fs.readFileSync(filePath, 'utf-8')
    const originalContent = content
    let needsLoggerImport = false
    
    // Apply replacements
    for (const { pattern, replacement, needsImport } of REPLACEMENTS) {
      if (pattern.test(content)) {
        content = content.replace(pattern, replacement)
        if (needsImport) {
          needsLoggerImport = true
        }
      }
    }
    
    // If content changed, update file
    if (content !== originalContent) {
      // Add logger import if needed
      if (needsLoggerImport && !hasLoggerImport(content)) {
        content = addLoggerImport(content, filePath)
      }
      
      fs.writeFileSync(filePath, content)
      return true
    }
    
    return false
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error)
    return false
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🔍 Searching for console.log statements...\n')
  
  // Find all TypeScript/JavaScript files
  const files = await glob('**/*.{ts,tsx,js,jsx}', {
    cwd: SRC_DIR,
    ignore: SKIP_PATTERNS
  })
  
  let processedCount = 0
  let modifiedCount = 0
  
  for (const file of files) {
    const filePath = path.join(SRC_DIR, file)
    processedCount++
    
    if (await processFile(filePath)) {
      console.log(`✅ Updated: ${file}`)
      modifiedCount++
    }
  }
  
  console.log(`
Summary:
- Files processed: ${processedCount}
- Files modified: ${modifiedCount}
- Console statements replaced with logger service
  `)
  
  if (modifiedCount > 0) {
    console.log('⚠️  Please review the changes and ensure imports are correct')
    console.log('💡 Run your tests to ensure everything still works')
  }
}

// Run the script
main().catch(console.error)