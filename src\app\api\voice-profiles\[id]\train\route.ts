import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { VoiceProfileManager } from '@/lib/services/voice-profile-manager';
import { supabase } from '@/lib/supabase/client';
import { logger } from '@/lib/services/logger'

const voiceProfileManager = new VoiceProfileManager();

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabaseClient = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { texts, source = 'manual_entry' } = body;

    if (!texts || !Array.isArray(texts) || texts.length === 0) {
      return NextResponse.json(
        { error: 'Texts array is required' },
        { status: 400 }
      );
    }

    // Validate each text has minimum length
    const validTexts = texts.filter(text => 
      typeof text === 'string' && text.trim().length >= 100
    );

    if (validTexts.length === 0) {
      return NextResponse.json(
        { error: 'Each text sample must be at least 100 characters' },
        { status: 400 }
      );
    }

    // Check if user owns this profile
    const { data: profile, error: checkError } = await supabase
      .from('voice_profiles')
      .select('user_id')
      .eq('id', id)
      .single();

    if (checkError || !profile || profile.user_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Train the profile
    const success = await voiceProfileManager.trainVoiceProfile(
      id,
      validTexts,
      source
    );

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to train voice profile' },
        { status: 500 }
      );
    }

    // Fetch updated profile
    const { data: updatedProfile, error: fetchError } = await supabase
      .from('voice_profiles')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) throw fetchError;

    return NextResponse.json({
      success: true,
      profile: updatedProfile,
      trainedSamples: validTexts.length,
    });
  } catch (error) {
    logger.error('Error training voice profile:', error);
    return NextResponse.json(
      { error: 'Failed to train voice profile' },
      { status: 500 }
    );
  }
}