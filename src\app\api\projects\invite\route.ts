import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { requireAuth } from '@/lib/api/auth-middleware'
import { sendCollaborationInvite } from '@/lib/email'
import { logger } from '@/lib/services/logger'
import { v4 as uuidv4 } from 'uuid'

export async function POST(req: NextRequest) {
  // Check authentication
  const auth = await requireAuth(req)
  if (auth instanceof NextResponse) return auth
  
  try {
    const { projectId, email, role } = await req.json()
    
    if (!projectId || !email || !role) {
      return NextResponse.json(
        { error: 'Project ID, email, and role are required' },
        { status: 400 }
      )
    }
    
    if (!['editor', 'viewer'].includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be "editor" or "viewer"' },
        { status: 400 }
      )
    }
    
    const supabase = createClient()
    
    // Check if user owns the project or has team management permissions
    const { data: project } = await supabase
      .from('projects')
      .select('user_id, title')
      .eq('id', projectId)
      .single()
    
    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      )
    }
    
    const isOwner = project.user_id === auth.id
    
    if (!isOwner) {
      // Check if user has team management permissions
      const { data: collaborator } = await supabase
        .from('project_collaborators')
        .select('role')
        .eq('project_id', projectId)
        .eq('user_id', auth.id)
        .eq('status', 'active')
        .single()
      
      if (!collaborator || collaborator.role !== 'editor') {
        return NextResponse.json(
          { error: 'You do not have permission to invite team members' },
          { status: 403 }
        )
      }
    }
    
    // Check subscription limits
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('tier')
      .eq('user_id', project.user_id)
      .eq('status', 'active')
      .single()
    
    const maxCollaborators = subscription?.tier === 'studio' ? 5 : 
                           subscription?.tier === 'professional' ? 2 : 0
    
    if (maxCollaborators === 0) {
      return NextResponse.json(
        { error: 'Your subscription plan does not support collaboration' },
        { status: 403 }
      )
    }
    
    // Count current collaborators
    const { count } = await supabase
      .from('project_collaborators')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .eq('status', 'active')
    
    if (count && count >= maxCollaborators) {
      return NextResponse.json(
        { error: `You have reached the limit of ${maxCollaborators} collaborators for your plan` },
        { status: 403 }
      )
    }
    
    // Check if user is already a collaborator
    const { data: existingCollab } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single()
    
    if (existingCollab) {
      const { data: existing } = await supabase
        .from('project_collaborators')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', existingCollab.id)
        .single()
      
      if (existing) {
        return NextResponse.json(
          { error: 'This user is already a collaborator' },
          { status: 400 }
        )
      }
    }
    
    // Check if invitation already exists
    const { data: existingInvite } = await supabase
      .from('project_invitations')
      .select('id')
      .eq('project_id', projectId)
      .eq('email', email)
      .eq('status', 'pending')
      .gte('expires_at', new Date().toISOString())
      .single()
    
    if (existingInvite) {
      return NextResponse.json(
        { error: 'An invitation has already been sent to this email' },
        { status: 400 }
      )
    }
    
    // Create invitation token
    const token = uuidv4()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // 7 days expiry
    
    // Create invitation record
    const { error: inviteError } = await supabase
      .from('project_invitations')
      .insert({
        project_id: projectId,
        email,
        role,
        token,
        inviter_id: auth.id,
        status: 'pending',
        expires_at: expiresAt.toISOString()
      })
    
    if (inviteError) throw inviteError
    
    // Get inviter details
    const { data: inviter } = await supabase
      .from('users')
      .select('email, profiles(display_name)')
      .eq('id', auth.id)
      .single()
    
    const inviterName = inviter?.profiles?.display_name || inviter?.email || 'A BookScribe user'
    const inviteUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${token}`
    
    // Send invitation email
    await sendCollaborationInvite(
      email,
      project.title,
      inviterName,
      role,
      inviteUrl
    )
    
    logger.info('Collaboration invitation sent', {
      projectId,
      invitedEmail: email,
      role,
      inviterId: auth.id
    })
    
    return NextResponse.json({
      success: true,
      message: 'Invitation sent successfully'
    })
    
  } catch (error) {
    logger.error('Error sending invitation', error)
    return NextResponse.json(
      { error: 'Failed to send invitation' },
      { status: 500 }
    )
  }
}