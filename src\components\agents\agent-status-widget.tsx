import { logger } from '@/lib/services/logger'
'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Brain, 
  Activity, 
  CheckCircle2, 
  AlertCircle,
  Zap,
  Clock
} from 'lucide-react'
import Link from 'next/link'

interface AgentStatusWidgetProps {
  projectId: string
}

interface AgentStatus {
  isActive: boolean
  currentTask?: string
  progress?: number
  eta?: string
  completedTasks: number
  quality: number
}

export function AgentStatusWidget({ projectId }: AgentStatusWidgetProps) {
  const [status, setStatus] = useState<AgentStatus>({
    isActive: false,
    completedTasks: 0,
    quality: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate fetching agent status
    const checkStatus = async () => {
      try {
        // In a real implementation, this would fetch from the API
        setStatus({
          isActive: Math.random() > 0.7,
          currentTask: 'Analyzing chapter structure',
          progress: Math.floor(Math.random() * 100),
          eta: '5 minutes',
          completedTasks: Math.floor(Math.random() * 20),
          quality: 85 + Math.floor(Math.random() * 10)
        })
      } catch (error) {
        logger.error('Failed to fetch agent status:', error)
      } finally {
        setLoading(false)
      }
    }

    checkStatus()
    const interval = setInterval(checkStatus, 10000) // Check every 10 seconds
    
    return () => clearInterval(interval)
  }, [projectId])

  if (loading) return null

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          {status.isActive ? (
            <>
              <Activity className="h-4 w-4 text-green-500 animate-pulse" />
              <span className="hidden md:inline text-sm">Agents Active</span>
            </>
          ) : (
            <>
              <Brain className="h-4 w-4 text-muted-foreground" />
              <span className="hidden md:inline text-sm">Agents Idle</span>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI Agent Status
            </h4>
            <Link href={`/projects/${projectId}/agents`}>
              <Button variant="ghost" size="sm">View Dashboard</Button>
            </Link>
          </div>

          {/* Current Status */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Status</span>
              {status.isActive ? (
                <Badge variant="default" className="bg-green-500">
                  <Activity className="h-3 w-3 mr-1" />
                  Active
                </Badge>
              ) : (
                <Badge variant="secondary">
                  <Brain className="h-3 w-3 mr-1" />
                  Idle
                </Badge>
              )}
            </div>

            {status.isActive && status.currentTask && (
              <>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Current Task</p>
                  <p className="text-xs text-muted-foreground">{status.currentTask}</p>
                </div>
                
                {status.progress !== undefined && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Progress</span>
                      <span>{status.progress}%</span>
                    </div>
                    <Progress value={status.progress} className="h-2" />
                  </div>
                )}

                {status.eta && (
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>ETA: {status.eta}</span>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-4 pt-2 border-t">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                <span className="text-lg font-semibold">{status.completedTasks}</span>
              </div>
              <p className="text-xs text-muted-foreground">Tasks Today</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1">
                <Zap className="h-4 w-4 text-yellow-500" />
                <span className="text-lg font-semibold">{status.quality}%</span>
              </div>
              <p className="text-xs text-muted-foreground">Quality Score</p>
            </div>
          </div>

          {/* Action Button */}
          {!status.isActive && (
            <Link href={`/projects/${projectId}/agents`} className="block">
              <Button size="sm" className="w-full">
                <Brain className="h-4 w-4 mr-2" />
                Start AI Agents
              </Button>
            </Link>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}