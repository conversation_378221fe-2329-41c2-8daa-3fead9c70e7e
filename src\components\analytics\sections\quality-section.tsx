import { useState, useEffect } from 'react'
import { QualityMetrics } from '../components/quality-metrics'
import { ProgressChart } from '../components/progress-chart'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import { TrendingUp, TrendingDown, AlertTriangle, CheckCircle, BookOpen } from 'lucide-react'
import { logger } from '@/lib/services/logger'

interface QualityMetricsData {
  readability: number
  consistency: number
  pacing: number
  engagement: number
  dialogue: number
  description: number
}

interface QualityTrend {
  date: string
  score: number
}

interface QualityData {
  overallScore: number
  metrics: QualityMetricsData
  trends: QualityTrend[]
}

interface AnalyticsData {
  quality?: QualityData
}

interface Project {
  id: string
  title: string
  primary_genre: string
}

interface QualitySectionProps {
  data: AnalyticsData | null
  isLoading: boolean
  selectedProject?: string
  projects?: Project[]
}

export function QualitySection({ data, isLoading, selectedProject, projects }: QualitySectionProps) {
  const quality = data?.quality || {
    overallScore: 0,
    metrics: {
      readability: 0,
      consistency: 0,
      pacing: 0,
      engagement: 0,
      dialogue: 0,
      description: 0
    },
    trends: []
  }

  // Find best and worst metrics
  const metricsArray = Object.entries(quality.metrics).map(([key, value]) => ({
    name: key,
    score: value as number
  }))

  const bestMetric = metricsArray.reduce((best, current) => 
    current.score > best.score ? current : best
  )
  
  const worstMetric = metricsArray.reduce((worst, current) => 
    current.score < worst.score ? current : worst
  )

  // Generate insights based on metrics
  const insights = []
  
  if (quality.metrics.readability < 70) {
    insights.push({
      type: 'warning',
      title: 'Readability Concern',
      description: 'Consider simplifying complex sentences and using more common vocabulary.'
    })
  }
  
  if (quality.metrics.pacing < 70) {
    insights.push({
      type: 'warning',
      title: 'Pacing Issues',
      description: 'Review chapter lengths and scene transitions for better flow.'
    })
  }
  
  if (quality.metrics.dialogue > 85) {
    insights.push({
      type: 'success',
      title: 'Strong Dialogue',
      description: 'Your dialogue is engaging and authentic. Keep it up!'
    })
  }

  return (
    <>
      {/* Quality Metrics Visualization */}
      <div className="grid gap-6 lg:grid-cols-2">
        <QualityMetrics
          metrics={quality.metrics}
          overallScore={quality.overallScore}
          loading={isLoading}
          showRadar={true}
        />

        {/* Quality Insights */}
        <div className="space-y-4">
          {/* Best & Worst Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Highlights</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-green-500/10 rounded">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span className="font-medium capitalize">{bestMetric.name}</span>
                </div>
                <Badge variant="default" className="bg-green-500">
                  {bestMetric.score}% - Best
                </Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-red-500/10 rounded">
                <div className="flex items-center gap-2">
                  <TrendingDown className="h-4 w-4 text-red-500" />
                  <span className="font-medium capitalize">{worstMetric.name}</span>
                </div>
                <Badge variant="destructive">
                  {worstMetric.score}% - Needs Work
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Quality Alerts */}
          <div className="space-y-3">
            {insights.map((insight, index) => (
              <Alert key={index} variant={insight.type === 'warning' ? 'destructive' : 'default'}>
                {insight.type === 'warning' ? (
                  <AlertTriangle className="h-4 w-4" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  <strong>{insight.title}:</strong> {insight.description}
                </AlertDescription>
              </Alert>
            ))}
          </div>
        </div>
      </div>

      {/* Quality Trends */}
      {quality.trends.length > 0 && (
        <ProgressChart
          title="Quality Score Trends"
          data={quality.trends}
          lines={[
            { dataKey: 'score', color: 'hsl(var(--primary))', name: 'Overall Score' }
          ]}
          type="line"
          height={250}
          loading={isLoading}
          yAxisLabel="Score"
        />
      )}

      {/* Dynamic Improvement Recommendations */}
      <SmartRecommendations 
        selectedProject={selectedProject}
        projects={projects}
        isLoading={isLoading}
      />

      {/* Chapter Quality Breakdown */}
      <ChapterQualityBreakdown 
        projectId={selectedProject !== 'all' ? selectedProject : undefined}
        isLoading={isLoading}
      />
    </>
  )
}

interface ChapterQualityData {
  id: string
  title: string
  quality_score: number
  word_count: number
  last_analyzed: string
}

interface ChapterQualityBreakdownProps {
  projectId?: string
  isLoading: boolean
}

function ChapterQualityBreakdown({ projectId, isLoading }: ChapterQualityBreakdownProps) {
  const [chapters, setChapters] = useState<ChapterQualityData[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!projectId) {
      setChapters([])
      return
    }

    const fetchChapterQuality = async () => {
      setLoading(true)
      try {
        const response = await fetch(`/api/analytics/chapters?projectId=${projectId}`)
        if (response.ok) {
          const data = await response.json()
          setChapters(data.chapters || [])
        }
      } catch (error) {
        logger.error('Failed to fetch chapter quality:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchChapterQuality()
  }, [projectId])

  if (isLoading || loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Chapter Quality Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!projectId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Chapter Quality Analysis</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
          <p className="text-muted-foreground">Select a specific project to view chapter-by-chapter quality analysis</p>
        </CardContent>
      </Card>
    )
  }

  if (chapters.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Chapter Quality Analysis</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
          <p className="text-muted-foreground">No chapters analyzed yet</p>
          <p className="text-sm text-muted-foreground mt-1">
            Chapters will appear here after quality analysis is run
          </p>
        </CardContent>
      </Card>
    )
  }

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'text-green-500'
    if (score >= 80) return 'text-blue-500'
    if (score >= 70) return 'text-yellow-500'
    if (score >= 60) return 'text-orange-500'
    return 'text-red-500'
  }

  const getQualityBadge = (score: number) => {
    if (score >= 90) return { text: 'Excellent', variant: 'default' as const }
    if (score >= 80) return { text: 'Good', variant: 'secondary' as const }
    if (score >= 70) return { text: 'Fair', variant: 'outline' as const }
    if (score >= 60) return { text: 'Needs Work', variant: 'outline' as const }
    return { text: 'Poor', variant: 'destructive' as const }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Chapter Quality Analysis</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {chapters.map((chapter) => {
            const badge = getQualityBadge(chapter.quality_score)
            return (
              <div key={chapter.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <h4 className="font-medium">{chapter.title}</h4>
                    <Badge variant={badge.variant}>
                      {badge.text}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                    <span>{chapter.word_count.toLocaleString()} words</span>
                    <span>Last analyzed: {new Date(chapter.last_analyzed).toLocaleDateString()}</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-2xl font-bold ${getQualityColor(chapter.quality_score)}`}>
                    {chapter.quality_score}
                  </div>
                  <div className="text-xs text-muted-foreground">Quality Score</div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

interface BookRecommendation {
  id: string
  bookTitle: string
  category: 'readability' | 'consistency' | 'engagement' | 'dialogue'
  severity: 'critical' | 'high' | 'medium' | 'low'
  issue: string
  recommendation: string
  currentScore: number
  targetScore: number
}

interface SmartRecommendationsProps {
  selectedProject?: string
  projects?: Project[]
  isLoading: boolean
}

function SmartRecommendations({ selectedProject, projects, isLoading }: SmartRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<BookRecommendation[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const fetchRecommendations = async () => {
      if (isLoading) return
      
      setLoading(true)
      try {
        const url = selectedProject === 'all' 
          ? '/api/analytics/recommendations'
          : `/api/analytics/recommendations?projectId=${selectedProject}`
          
        const response = await fetch(url)
        if (response.ok) {
          const data = await response.json()
          setRecommendations(data.recommendations || [])
        }
      } catch (error) {
        logger.error('Failed to fetch recommendations:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchRecommendations()
  }, [selectedProject, isLoading])

  if (isLoading || loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Smart Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-20 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (recommendations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Smart Recommendations</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-3" />
          <p className="text-muted-foreground">Excellent work! No critical issues found.</p>
          <p className="text-sm text-muted-foreground mt-1">
            Your writing quality meets our standards across all metrics.
          </p>
        </CardContent>
      </Card>
    )
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'readability': return '📖'
      case 'consistency': return '🔄' 
      case 'engagement': return '✨'
      case 'dialogue': return '💬'
      default: return '📝'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'readability': return 'text-blue-500'
      case 'consistency': return 'text-purple-500'
      case 'engagement': return 'text-orange-500'
      case 'dialogue': return 'text-green-500'
      default: return 'text-gray-500'
    }
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical': return { variant: 'destructive' as const, text: 'Critical' }
      case 'high': return { variant: 'destructive' as const, text: 'High' }
      case 'medium': return { variant: 'outline' as const, text: 'Medium' }
      case 'low': return { variant: 'secondary' as const, text: 'Low' }
      default: return { variant: 'secondary' as const, text: 'Info' }
    }
  }

  // Group recommendations by category for display
  const groupedRecommendations = recommendations.reduce((groups, rec) => {
    if (!groups[rec.category]) {
      groups[rec.category] = []
    }
    groups[rec.category].push(rec)
    return groups
  }, {} as Record<string, BookRecommendation[]>)

  return (
    <Card>
      <CardHeader>
        <CardTitle>Smart Recommendations</CardTitle>
        <p className="text-sm text-muted-foreground">
          {selectedProject === 'all' 
            ? `Top priority recommendations across ${projects?.length || 0} projects`
            : 'Detailed recommendations for selected project'
          }
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(groupedRecommendations).map(([category, recs]) => (
            <div key={category} className="space-y-3">
              <div className="flex items-center gap-2 border-b pb-2">
                <span className="text-lg">{getCategoryIcon(category)}</span>
                <h4 className={`font-medium capitalize ${getCategoryColor(category)}`}>
                  {category} Issues
                </h4>
                <Badge variant="outline" className="ml-auto">
                  {recs.length} {recs.length === 1 ? 'issue' : 'issues'}
                </Badge>
              </div>
              
              <div className="space-y-3">
                {recs.map((rec) => {
                  const severityBadge = getSeverityBadge(rec.severity)
                  return (
                    <div key={rec.id} className="p-4 border rounded-lg space-y-2">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h5 className="font-medium">{rec.bookTitle}</h5>
                            <Badge variant={severityBadge.variant} className="text-xs">
                              {severityBadge.text}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">{rec.issue}</p>
                          <p className="text-sm font-medium">{rec.recommendation}</p>
                        </div>
                        <div className="text-right ml-4">
                          <div className="text-sm text-muted-foreground">Score</div>
                          <div className="text-lg font-bold">
                            {rec.currentScore} → {rec.targetScore}
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}