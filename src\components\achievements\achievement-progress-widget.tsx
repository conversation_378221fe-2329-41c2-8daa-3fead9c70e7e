import { logger } from '@/lib/services/logger'
'use client'

import { useState, useEffect } from 'react'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Trophy, Star, TrendingUp } from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'
import Link from 'next/link'

interface QuickStats {
  totalUnlocked: number
  totalAchievements: number
  recentAchievement?: {
    name: string
    unlockedAt: string
  }
  nextMilestone?: {
    name: string
    progress: number
    requirement: number
  }
}

export function AchievementProgressWidget() {
  const { user } = useAuth()
  const [stats, setStats] = useState<QuickStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchQuickStats()
    }
  }, [user])

  const fetchQuickStats = async () => {
    try {
      const response = await fetch(`/api/achievements/stats?userId=${user?.id}`)
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      logger.error('Failed to fetch achievement stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!user || loading || !stats) return null

  const progressPercentage = (stats.totalUnlocked / stats.totalAchievements) * 100

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          <Trophy className="h-4 w-4 text-yellow-500" />
          <span className="hidden md:inline text-sm">
            {stats.totalUnlocked}/{stats.totalAchievements}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold">Achievements</h4>
            <Link href="/achievements">
              <Button variant="ghost" size="sm">View All</Button>
            </Link>
          </div>

          {/* Overall Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span className="text-muted-foreground">
                {Math.round(progressPercentage)}%
              </span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
            <p className="text-xs text-muted-foreground">
              {stats.totalUnlocked} of {stats.totalAchievements} achievements unlocked
            </p>
          </div>

          {/* Recent Achievement */}
          {stats.recentAchievement && (
            <div className="pt-2 border-t">
              <div className="flex items-center gap-2 text-sm">
                <Star className="h-4 w-4 text-yellow-500" />
                <div className="flex-1">
                  <p className="font-medium">Latest Achievement</p>
                  <p className="text-xs text-muted-foreground">
                    {stats.recentAchievement.name}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Next Milestone */}
          {stats.nextMilestone && (
            <div className="pt-2 border-t">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span className="font-medium">Next Milestone</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {stats.nextMilestone.name}
                </p>
                <div className="space-y-1">
                  <Progress 
                    value={(stats.nextMilestone.progress / stats.nextMilestone.requirement) * 100} 
                    className="h-2" 
                  />
                  <p className="text-xs text-muted-foreground text-right">
                    {stats.nextMilestone.progress} / {stats.nextMilestone.requirement}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}