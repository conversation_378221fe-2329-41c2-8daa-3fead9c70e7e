import { logger } from './services/logger'
import { sendMailerooEmail, sendCollaborationInviteViaMaileroo } from './email/maileroo'
import { verifyEmailWithZeruh, sanitizeEmail } from './email/zeruh'

interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
  from?: string
}

/**
 * Send email using configured email service
 * This is the main email sending function that should be used throughout the app
 */
export async function sendEmail(options: EmailOptions): Promise<void> {
  const { to, subject, html, text, from = 'BookScribe AI <<EMAIL>>' } = options
  
  try {
    // Verify email before sending (only in production)
    if (process.env.NODE_ENV === 'production') {
      const verification = await verifyEmailWithZeruh(to)
      if (!verification.isValid) {
        logger.warn('Email verification failed', {
          email: to,
          reason: verification.reason
        })
        throw new Error(`Invalid email address: ${verification.reason}`)
      }
    }
    
    // Send email via Maileroo
    const result = await sendMailerooEmail({
      to: sanitizeEmail(to),
      subject,
      html,
      text,
      from
    })
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to send email')
    }
    
    logger.info('Email sent successfully', {
      to,
      subject,
      messageId: result.message_id
    })
    
  } catch (error) {
    logger.error('Failed to send email', error)
    throw error
  }
}

/**
 * Send collaboration invitation email
 */
export async function sendCollaborationInvite(
  email: string,
  projectTitle: string,
  inviterName: string,
  role: 'editor' | 'viewer',
  inviteUrl: string
): Promise<void> {
  try {
    // Verify email before sending
    const verification = await verifyEmailWithZeruh(email)
    if (!verification.isValid) {
      throw new Error(`Cannot send invitation: ${verification.reason}`)
    }
    
    // Send via Maileroo
    const result = await sendCollaborationInviteViaMaileroo(
      sanitizeEmail(email),
      projectTitle,
      inviterName,
      role,
      inviteUrl
    )
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to send collaboration invitation')
    }
    
    logger.info('Collaboration invitation sent', {
      to: email,
      projectTitle,
      messageId: result.message_id
    })
    
  } catch (error) {
    logger.error('Failed to send collaboration invitation', error)
    throw error
  }
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(
  email: string,
  resetUrl: string
): Promise<void> {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .container {
          background-color: #ffffff;
          border-radius: 8px;
          padding: 40px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .logo {
          text-align: center;
          margin-bottom: 30px;
        }
        .logo h1 {
          color: #8B4513;
          margin: 0;
        }
        .button {
          display: inline-block;
          padding: 12px 30px;
          background-color: #8B4513;
          color: white;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 600;
          margin: 20px 0;
        }
        .footer {
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid #eee;
          font-size: 14px;
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="logo">
          <h1>BookScribe AI</h1>
        </div>
        
        <h2>Reset Your Password</h2>
        
        <p>Hi there,</p>
        
        <p>We received a request to reset your BookScribe password. Click the button below to create a new password:</p>
        
        <div style="text-align: center;">
          <a href="${resetUrl}" class="button">Reset Password</a>
        </div>
        
        <p>This link will expire in 1 hour for security reasons.</p>
        
        <p>If you didn't request a password reset, you can safely ignore this email. Your password won't be changed.</p>
        
        <div class="footer">
          <p>Best regards,<br>The BookScribe Team</p>
          <p>If the button doesn't work, copy and paste this link into your browser:<br>${resetUrl}</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  await sendEmail({
    to: email,
    subject: 'Reset Your BookScribe Password',
    html,
    from: 'BookScribe AI <<EMAIL>>'
  })
}

/**
 * Send welcome email to new users
 */
export async function sendWelcomeEmail(
  email: string,
  userName?: string
): Promise<void> {
  const name = userName || 'Writer'
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #8B4513;
          color: white;
          padding: 40px;
          text-align: center;
          border-radius: 8px 8px 0 0;
        }
        .content {
          background-color: #ffffff;
          padding: 40px;
          border-radius: 0 0 8px 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .feature {
          margin: 20px 0;
          padding: 20px;
          background-color: #f9f7f5;
          border-radius: 6px;
        }
        .button {
          display: inline-block;
          padding: 14px 32px;
          background-color: #8B4513;
          color: white;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 600;
          margin: 20px 0;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Welcome to BookScribe AI!</h1>
        <p>Your AI-powered writing journey begins now</p>
      </div>
      
      <div class="content">
        <h2>Hi ${name}! 👋</h2>
        
        <p>Welcome to BookScribe AI! We're thrilled to have you join our community of writers who are crafting amazing stories with the help of AI.</p>
        
        <div class="feature">
          <h3>🚀 Getting Started</h3>
          <p>Here's how to make the most of BookScribe:</p>
          <ul>
            <li><strong>Create Your First Project:</strong> Start with a story idea and let our AI help you develop it</li>
            <li><strong>Explore AI Agents:</strong> Meet your writing assistants who specialize in different aspects of storytelling</li>
            <li><strong>Build Your Story Bible:</strong> Keep track of characters, locations, and plot threads</li>
            <li><strong>Write with AI:</strong> Get suggestions, overcome writer's block, and maintain consistency</li>
          </ul>
        </div>
        
        <div style="text-align: center;">
          <a href="${process.env.NEXT_PUBLIC_APP_URL}/projects/new" class="button">Create Your First Project</a>
        </div>
        
        <div class="feature">
          <h3>💡 Pro Tips</h3>
          <ul>
            <li>Start with the Story Architect to outline your plot</li>
            <li>Use the Character Developer to create memorable characters</li>
            <li>Let the Writing Agent help you craft engaging chapters</li>
            <li>Check out our tutorials for advanced techniques</li>
          </ul>
        </div>
        
        <p>If you have any questions, our support team is here to help at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
        
        <p>Happy writing!</p>
        <p>The BookScribe Team</p>
      </div>
    </body>
    </html>
  `
  
  await sendEmail({
    to: email,
    subject: 'Welcome to BookScribe AI! 🎉',
    html,
    from: 'BookScribe AI <<EMAIL>>'
  })
}

/**
 * Send project export completion email
 */
export async function sendExportCompleteEmail(
  email: string,
  projectTitle: string,
  downloadUrl: string,
  format: string
): Promise<void> {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .container {
          background-color: #ffffff;
          border-radius: 8px;
          padding: 40px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .button {
          display: inline-block;
          padding: 12px 30px;
          background-color: #8B4513;
          color: white;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 600;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h2>Your Export is Ready! 📚</h2>
        
        <p>Great news! Your export of <strong>"${projectTitle}"</strong> in ${format.toUpperCase()} format is ready for download.</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${downloadUrl}" class="button">Download Your ${format.toUpperCase()}</a>
        </div>
        
        <p><strong>Note:</strong> This download link will expire in 24 hours for security reasons.</p>
        
        <p>Happy writing!</p>
        <p>The BookScribe Team</p>
      </div>
    </body>
    </html>
  `
  
  await sendEmail({
    to: email,
    subject: `Your "${projectTitle}" export is ready!`,
    html
  })
}