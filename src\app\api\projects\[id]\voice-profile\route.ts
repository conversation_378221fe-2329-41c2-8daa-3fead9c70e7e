import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { supabase } from '@/lib/supabase/client';
import { VoiceProfileManager } from '@/lib/services/voice-profile-manager';
import { logger } from '@/lib/services/logger'

const voiceProfileManager = new VoiceProfileManager();

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params;
    const supabaseClient = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get project with voice profile
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        voice_profile_id,
        voice_profile:voice_profiles(
          id,
          name,
          description,
          type,
          patterns,
          confidence,
          training_samples_count,
          total_words_analyzed,
          created_at,
          updated_at
        )
      `)
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // If no voice profile assigned, try to get user's default author profile
    if (!project.voice_profile) {
      const profiles = await voiceProfileManager.getUserVoiceProfiles(user.id, {
        type: 'author',
        isGlobal: true
      });

      if (profiles.length > 0) {
        return NextResponse.json({
          profile: profiles[0],
          isDefault: true
        });
      }
    }

    return NextResponse.json({
      profile: project.voice_profile,
      isDefault: false
    });
  } catch (error) {
    logger.error('Error fetching project voice profile:', error);
    return NextResponse.json(
      { error: 'Failed to fetch voice profile' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params;
    const supabaseClient = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { profile, voiceProfileId } = body;

    // Verify user owns this project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', projectId)
      .single();

    if (projectError || !project || project.user_id !== user.id) {
      return NextResponse.json({ error: 'Project not found or unauthorized' }, { status: 404 });
    }

    if (profile) {
      // Create a new voice profile for this project
      const newProfile = await voiceProfileManager.createVoiceProfile({
        name: `${profile.name || 'Project Voice'}`,
        description: profile.description,
        type: profile.type || 'author',
        projectId: projectId
      });

      if (!newProfile) {
        return NextResponse.json(
          { error: 'Failed to create voice profile' },
          { status: 500 }
        );
      }

      // Update project with new profile
      const { error: updateError } = await supabase
        .from('projects')
        .update({ voice_profile_id: newProfile.id })
        .eq('id', projectId);

      if (updateError) throw updateError;

      return NextResponse.json({ profile: newProfile });
    } else if (voiceProfileId) {
      // Verify user owns the voice profile
      const { data: voiceProfile, error: profileError } = await supabase
        .from('voice_profiles')
        .select('user_id')
        .eq('id', voiceProfileId)
        .single();

      if (profileError || !voiceProfile || voiceProfile.user_id !== user.id) {
        return NextResponse.json({ error: 'Voice profile not found or unauthorized' }, { status: 404 });
      }

      // Update project with existing profile
      const { error: updateError } = await supabase
        .from('projects')
        .update({ voice_profile_id: voiceProfileId })
        .eq('id', projectId);

      if (updateError) throw updateError;

      // Fetch and return the updated profile
      const { data: updatedProject, error: fetchError } = await supabase
        .from('projects')
        .select(`
          voice_profile:voice_profiles(
            id,
            name,
            description,
            type,
            patterns,
            confidence,
            training_samples_count,
            total_words_analyzed,
            created_at,
            updated_at
          )
        `)
        .eq('id', projectId)
        .single();

      if (fetchError) throw fetchError;

      return NextResponse.json({ profile: updatedProject.voice_profile });
    } else {
      return NextResponse.json(
        { error: 'Either profile data or voiceProfileId is required' },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error('Error updating project voice profile:', error);
    return NextResponse.json(
      { error: 'Failed to update voice profile' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params;
    const supabaseClient = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify user owns this project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', projectId)
      .single();

    if (projectError || !project || project.user_id !== user.id) {
      return NextResponse.json({ error: 'Project not found or unauthorized' }, { status: 404 });
    }

    // Remove voice profile assignment
    const { error: updateError } = await supabase
      .from('projects')
      .update({ voice_profile_id: null })
      .eq('id', projectId);

    if (updateError) throw updateError;

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error('Error removing project voice profile:', error);
    return NextResponse.json(
      { error: 'Failed to remove voice profile' },
      { status: 500 }
    );
  }
}