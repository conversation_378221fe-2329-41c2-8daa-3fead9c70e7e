'use client'

import React from 'react'
import { logger } from '@/lib/services/logger';

import { ErrorBoundary } from './error-boundary'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { 
  AlertTriangle, 
  RefreshCw, 
 
  WifiOff, 
  Clock, 
  Shield,
  Server,
  Home
} from 'lucide-react'
import { config } from '@/lib/config'

interface APIError extends Error {
  status?: number
  code?: string
  details?: unknown
}

interface APIErrorBoundaryProps {
  children: React.ReactNode
  onRetry?: () => void
  retryable?: boolean
  fallbackComponent?: React.ComponentType<{ error: APIError; retry: () => void }>
  showErrorDetails?: boolean
}

interface APIErrorDisplayProps {
  error: APIError
  onRetry?: () => void
  retryable?: boolean
  showErrorDetails?: boolean
}

function APIErrorDisplay({ error, onRetry, retryable = true, showErrorDetails }: APIErrorDisplayProps) {
  const getErrorInfo = (error: APIError) => {
    const status = error.status || 500
    
    switch (true) {
      case status === 401:
        return {
          title: 'Authentication Required',
          description: 'Please sign in to continue accessing BookScribe.',
          icon: Shield,
          action: 'Sign In',
          actionFn: () => window.location.href = '/login',
          variant: 'default' as const
        }
      
      case status === 403:
        return {
          title: 'Access Denied',
          description: 'You don\'t have permission to access this resource.',
          icon: Shield,
          action: 'Go to Dashboard',
          actionFn: () => window.location.href = '/dashboard',
          variant: 'destructive' as const
        }
      
      case status === 404:
        return {
          title: 'Not Found',
          description: 'The resource you\'re looking for doesn\'t exist or has been moved.',
          icon: AlertTriangle,
          action: 'Go Back',
          actionFn: () => window.history.back(),
          variant: 'default' as const
        }
      
      case status === 429:
        return {
          title: 'Rate Limited',
          description: 'Too many requests. Please wait a moment before trying again.',
          icon: Clock,
          action: 'Try Again',
          actionFn: onRetry,
          variant: 'default' as const
        }
      
      case status >= 500:
        return {
          title: 'Server Error',
          description: 'Our servers are experiencing issues. We\'re working to fix this.',
          icon: Server,
          action: 'Try Again',
          actionFn: onRetry,
          variant: 'destructive' as const
        }
      
      case status === 0 || error.message.includes('fetch'):
        return {
          title: 'Connection Error',
          description: 'Unable to connect to BookScribe. Please check your internet connection.',
          icon: WifiOff,
          action: 'Retry Connection',
          actionFn: onRetry,
          variant: 'default' as const
        }
      
      default:
        return {
          title: 'Request Failed',
          description: error.message || 'An unexpected error occurred while processing your request.',
          icon: AlertTriangle,
          action: 'Try Again',
          actionFn: onRetry,
          variant: 'destructive' as const
        }
    }
  }

  const errorInfo = getErrorInfo(error)
  const Icon = errorInfo.icon

  return (
    <div className="flex items-center justify-center p-4 min-h-[200px]">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
            <Icon className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle>{errorInfo.title}</CardTitle>
          <CardDescription>
            {errorInfo.description}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error.status && (
            <Alert variant={errorInfo.variant}>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>HTTP {error.status}</AlertTitle>
              <AlertDescription>
                {error.code && `Error Code: ${error.code}`}
              </AlertDescription>
            </Alert>
          )}

          {showErrorDetails && config.isDevelopment && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Technical Details</AlertTitle>
              <AlertDescription>
                <pre className="text-xs mt-2 p-2 bg-muted rounded overflow-auto">
                  {JSON.stringify({
                    message: error.message,
                    status: error.status,
                    code: error.code,
                    details: error.details
                  }, null, 2)}
                </pre>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            {retryable && errorInfo.actionFn && (
              <Button 
                onClick={errorInfo.actionFn}
                className="flex-1"
                variant="outline"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {errorInfo.action}
              </Button>
            )}
            
            <Button 
              onClick={() => window.location.href = '/dashboard'}
              className="flex-1"
            >
              <Home className="h-4 w-4 mr-2" />
              Dashboard
            </Button>
          </div>

          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              If this problem persists, please contact support.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export function APIErrorBoundary({ 
  children, 
  onRetry, 
  retryable = true,
  fallbackComponent: FallbackComponent,
  showErrorDetails 
}: APIErrorBoundaryProps) {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Enhanced API error logging
    if (config.isDevelopment) {
      logger.error('🚨 API Error Boundary', { 
        error: error.message,
        stack: error.stack,
        errorInfo: errorInfo.componentStack
      });
    }

    // Report API errors with additional context
    if (config.isProduction) {
      fetch('/api/errors/report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'api_error',
          message: error.message,
          stack: error.stack,
          status: (error as APIError).status,
          code: (error as APIError).code,
          componentStack: errorInfo.componentStack,
          timestamp: new Date().toISOString(),
          url: window.location.href
        })
      }).catch(err => logger.error('Failed to report error', err))
    }
  }

  const fallback = FallbackComponent ? (
    <FallbackComponent 
      error={new Error() as APIError} 
      retry={onRetry || (() => window.location.reload())} 
    />
  ) : (
    <APIErrorDisplay
      error={new Error() as APIError}
      onRetry={onRetry}
      retryable={retryable}
      showErrorDetails={showErrorDetails}
    />
  )

  return (
    <ErrorBoundary
      fallback={fallback}
      onError={handleError}
      showErrorDetails={showErrorDetails}
      isolate={true}
    >
      {children}
    </ErrorBoundary>
  )
}

// Hook for handling async API errors
export function useAPIErrorHandler() {
  const handleError = React.useCallback((error: unknown) => {
    let apiError: APIError

    if (error instanceof Response) {
      apiError = new Error(`Request failed: ${error.statusText}`) as APIError
      apiError.status = error.status
    } else if (error instanceof Error) {
      apiError = error as APIError
    } else {
      apiError = new Error('Unknown API error') as APIError
    }

    // Throw error to be caught by ErrorBoundary
    throw apiError
  }, [])

  return { handleError }
}

// Specialized error boundary for specific API operations
export function withAPIErrorBoundary<T extends object>(
  Component: React.ComponentType<T>,
  options?: Omit<APIErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: T) => (
    <APIErrorBoundary {...options}>
      <Component {...props} />
    </APIErrorBoundary>
  )

  WrappedComponent.displayName = `withAPIErrorBoundary(${Component.displayName || Component.name})`
  return WrappedComponent
}