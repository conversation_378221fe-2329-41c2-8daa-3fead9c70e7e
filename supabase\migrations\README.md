# Supabase Migration Guide

## Overview
This directory contains all database migrations for BookScribe. Migrations are applied in order based on their numeric prefix or timestamp.

## Migration Order

### Numbered Migrations (Core Schema)
1. `001_enhanced_schema.sql` - Core tables: users, projects, chapters, characters, profiles
2. `002_profile_enhancements.sql` - Enhanced writing profiles and AI settings
3. `003_series_management.sql` - Series and multi-project support
4. `004_voice_profiles.sql` - Voice analysis and profile management
5. `005_universe_character_sharing.sql` - Universe creation and character sharing
6. `006_word_usage_tracking.sql` - Usage tracking for subscriptions
7. `007_embedding_cache.sql` - Cache for AI embeddings
8. `008_content_embeddings.sql` - Content similarity and search
9. `009_missing_tables.sql` - Additional tables (references, suggestions, etc.)
10. `010_writing_goals.sql` - Writing goals and progress tracking

### Timestamped Migrations (Feature Additions)
- `20240108_collaboration_tables.sql` - Real-time collaboration infrastructure
- `20240109_project_collaborators.sql` - Team member management
- `20250118_create_book_summaries.sql` - Book summary generation
- `20250119_*` - Various analytics and quality features
- `simple_stripe_tables.sql` - Stripe payment integration
- `stripe_sync_schema.sql` - Stripe synchronization tables

## Dependencies

### Core Dependencies
- PostgreSQL 13+
- Supabase Auth
- Supabase Realtime (for collaboration features)
- pgvector extension (for embeddings)

### Migration Dependencies
- `002_profile_enhancements.sql` depends on `001_enhanced_schema.sql` (users, projects)
- `003_series_management.sql` depends on `001_enhanced_schema.sql` (projects)
- `004_voice_profiles.sql` depends on `001_enhanced_schema.sql` (users, projects)
- `005_universe_character_sharing.sql` depends on `003_series_management.sql` (series)
- Collaboration tables depend on `001_enhanced_schema.sql` (users, projects)
- Analytics tables depend on core schema tables

## Running Migrations

### Local Development
```bash
# Using Supabase CLI
supabase db reset  # Resets database and runs all migrations
supabase migration up  # Runs pending migrations

# Manual execution (not recommended)
psql $DATABASE_URL -f migrations/001_enhanced_schema.sql
```

### Production
Migrations are automatically applied via Supabase Dashboard when pushed to production.

## Creating New Migrations

### Naming Convention
- Use sequential numbers for core schema changes: `011_feature_name.sql`
- Use timestamps for feature additions: `YYYYMMDD_feature_description.sql`

### Best Practices
1. Always include IF NOT EXISTS checks for tables
2. Use CASCADE when dropping constraints
3. Add comments to complex queries
4. Test migrations locally first
5. Include rollback instructions in comments

### Template
```sql
-- Migration: Brief description
-- Dependencies: List any required previous migrations
-- Rollback: Instructions to undo this migration

BEGIN;

-- Your migration SQL here

COMMIT;
```

## Troubleshooting

### Common Issues
1. **Duplicate key violations**: Check if migration was partially applied
2. **Missing dependencies**: Ensure migrations run in correct order
3. **Permission errors**: Verify service role key is used for admin operations

### Rollback Strategy
Each migration should include rollback instructions in comments. For production:
1. Create a new migration that undoes changes
2. Never modify existing migrations
3. Always backup before major schema changes