import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { bulkStoryBibleOperationSchema, createStoryBibleEntrySchema, updateStoryBibleEntrySchema } from '@/lib/validation/schemas'
import { z } from 'zod'
import type { StoryBible, Database } from '@/lib/db/types'
import type { SupabaseClient } from '@supabase/supabase-js'
import { generalLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter'
import { logger } from '@/lib/services/logger'

// Type definitions for bulk operations
interface BulkOperationResult {
  successful: BulkEntryResult[];
  failed: BulkEntryError[];
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

interface BulkEntryResult {
  index: number;
  entry: StoryBible | Partial<StoryBible>;
  operation: string;
  message?: string;
}

interface BulkEntryError {
  index: number;
  entry: unknown;
  error: string;
}

interface UpdateEntryData {
  id: string;
  [key: string]: unknown;
}

interface DeleteEntryData {
  id: string;
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for bulk operations (10 bulk operations per hour)
    const clientIP = getClientIP(request);
    const rateLimitResult = generalLimiter.check(10, clientIP);
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset);
    }

    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate input
    const validatedData = bulkStoryBibleOperationSchema.parse(body)
    const { operation, entries } = validatedData

    const results: BulkOperationResult = {
      successful: [],
      failed: [],
      summary: {
        total: entries.length,
        successful: 0,
        failed: 0
      }
    }

    // Process each entry based on operation type
    for (const [index, entryData] of entries.entries()) {
      try {
        let result
        
        switch (operation) {
          case 'create':
            result = await handleCreateEntry(supabase, user.id, entryData, index)
            break
          case 'update':
            result = await handleUpdateEntry(supabase, user.id, entryData, index)
            break
          case 'delete':
            result = await handleDeleteEntry(supabase, user.id, entryData, index)
            break
          default:
            throw new Error('Invalid operation')
        }
        
        results.successful.push(result)
        results.summary.successful++
        
      } catch (error) {
        logger.error(`Error in bulk ${operation} for entry ${index}:`, error)
        results.failed.push({
          index,
          entry: entryData,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        results.summary.failed++
      }
    }

    const statusCode = results.summary.failed === 0 ? 200 : 207 // Multi-status if some failed

    return NextResponse.json({
      operation,
      results,
      message: `Bulk ${operation} completed: ${results.summary.successful} successful, ${results.summary.failed} failed`
    }, { status: statusCode })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid bulk operation data',
        details: error.errors
      }, { status: 400 })
    }

    logger.error('Error in story bible bulk operation:', error)
    return NextResponse.json(
      { error: 'Failed to execute bulk operation' },
      { status: 500 }
    )
  }
}

async function handleCreateEntry(supabase: SupabaseClient<Database>, userId: string, entryData: unknown, index: number): Promise<BulkEntryResult> {
  // Validate entry data
  const validatedEntry = createStoryBibleEntrySchema.parse(entryData)

  // Verify project ownership
  const { data: project } = await supabase
    .from('projects')
    .select('id')
    .eq('id', validatedEntry.project_id)
    .eq('user_id', userId)
    .single()

  if (!project) {
    throw new Error(`Project not found for entry at index ${index}`)
  }

  // Check if entry_key already exists for this project and entry_type
  const { data: existingEntry } = await supabase
    .from('story_bible')
    .select('id')
    .eq('project_id', validatedEntry.project_id)
    .eq('entry_type', validatedEntry.entry_type)
    .eq('entry_key', validatedEntry.entry_key)
    .single()

  if (existingEntry) {
    throw new Error(`Entry with key "${validatedEntry.entry_key}" of type "${validatedEntry.entry_type}" already exists in project`)
  }

  // Create entry
  const { data: entry, error } = await supabase
    .from('story_bible')
    .insert({
      ...validatedEntry,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select()
    .single()

  if (error) {
    throw new Error(error.message)
  }

  return { index, entry, operation: 'create' }
}

async function handleUpdateEntry(supabase: SupabaseClient<Database>, userId: string, entryData: unknown, index: number): Promise<BulkEntryResult> {
  const data = entryData as UpdateEntryData
  if (!data.id) {
    throw new Error(`Entry ID required for update at index ${index}`)
  }

  const { id, ...updateData } = data
  const validatedUpdate = updateStoryBibleEntrySchema.parse(updateData)

  // Verify ownership through project
  const { data: ownershipCheck } = await supabase
    .from('story_bible')
    .select(`
      project_id,
      entry_key,
      entry_type,
      projects!inner (
        user_id
      )
    `)
    .eq('id', id)
    .eq('projects.user_id', userId)
    .single()

  if (!ownershipCheck) {
    throw new Error(`Story bible entry not found or access denied for ID ${id}`)
  }

  // If entry_key or entry_type is being updated, check for conflicts
  if ((validatedUpdate.entry_key && validatedUpdate.entry_key !== ownershipCheck.entry_key) ||
      (validatedUpdate.entry_type && validatedUpdate.entry_type !== ownershipCheck.entry_type)) {
    
    const newKey = validatedUpdate.entry_key || ownershipCheck.entry_key
    const newType = validatedUpdate.entry_type || ownershipCheck.entry_type
    
    const { data: existingEntry } = await supabase
      .from('story_bible')
      .select('id')
      .eq('project_id', ownershipCheck.project_id)
      .eq('entry_type', newType)
      .eq('entry_key', newKey)
      .neq('id', id)
      .single()

    if (existingEntry) {
      throw new Error(`Entry with key "${newKey}" of type "${newType}" already exists in project`)
    }
  }

  // Update entry
  const { data: entry, error } = await supabase
    .from('story_bible')
    .update({
      ...validatedUpdate,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    throw new Error(error.message)
  }

  return { index, entry, operation: 'update' }
}

async function handleDeleteEntry(supabase: SupabaseClient<Database>, userId: string, entryData: unknown, index: number): Promise<BulkEntryResult> {
  const data = entryData as DeleteEntryData
  if (!data.id) {
    throw new Error(`Entry ID required for delete at index ${index}`)
  }

  const { id } = data

  // Verify ownership through project
  const { data: entry } = await supabase
    .from('story_bible')
    .select(`
      id,
      entry_key,
      entry_type,
      projects!inner (
        user_id
      )
    `)
    .eq('id', id)
    .eq('projects.user_id', userId)
    .single()

  if (!entry) {
    throw new Error(`Story bible entry not found or access denied for ID ${id}`)
  }

  // Delete entry
  const { error } = await supabase
    .from('story_bible')
    .delete()
    .eq('id', id)

  if (error) {
    throw new Error(error.message)
  }

  return { 
    index, 
    entry: { 
      id: entry.id,
      entry_key: entry.entry_key,
      entry_type: entry.entry_type
    }, 
    operation: 'delete',
    message: `Entry "${entry.entry_key}" (${entry.entry_type}) deleted successfully`
  }
}